#!/usr/bin/env python3
"""
詳細測試 PyPtt 的文章和推文功能
"""

import sys
import os
import time
import json
import inspect
from datetime import datetime

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config

def test_pyptt_post_structure():
    """詳細測試 PyPtt 文章結構"""
    print("=" * 60)
    print("詳細測試 PyPtt 文章結構")
    print("=" * 60)
    
    try:
        import PyPtt
        
        # 建立 PyPtt 物件
        ptt_bot = PyPtt.API()
        
        print("1. 登入 PTT...")
        ptt_bot.login(
            ptt_id=Config.PTT_USERNAME,
            ptt_pw=Config.PTT_PASSWORD,
            kick_other_session=True
        )
        print("✅ 登入成功")
        
        # 測試不同看板
        boards_to_test = ["Test", "Gossiping"]
        
        for board_name in boards_to_test:
            print(f"\n{'='*40}")
            print(f"測試看板: {board_name}")
            print(f"{'='*40}")
            
            try:
                # 取得最新索引
                newest_index = ptt_bot.get_newest_index(
                    index_type=PyPtt.NewIndex.BOARD,
                    board=board_name
                )
                print(f"✅ 最新索引: {newest_index}")
                
                # 測試取得文章的不同方法
                for i in range(max(1, newest_index - 2), newest_index + 1):
                    print(f"\n--- 文章索引 {i} ---")
                    
                    try:
                        # 方法1: get_post
                        post = ptt_bot.get_post(
                            board=board_name,
                            index=i
                        )
                        
                        if post:
                            print(f"✅ get_post 成功")
                            print(f"文章類型: {type(post)}")
                            
                            if isinstance(post, dict):
                                print("文章字典內容:")
                                for key, value in post.items():
                                    print(f"  {key}: {value} (類型: {type(value)})")
                            else:
                                print("文章物件屬性:")
                                for attr in dir(post):
                                    if not attr.startswith('_'):
                                        try:
                                            value = getattr(post, attr)
                                            if not callable(value):
                                                print(f"  {attr}: {value} (類型: {type(value)})")
                                        except:
                                            print(f"  {attr}: 無法取得值")
                            
                            # 如果有 AID，嘗試其他方法
                            aid = None
                            if isinstance(post, dict):
                                aid = post.get('aid') or post.get('AID')
                            else:
                                aid = getattr(post, 'aid', None) or getattr(post, 'AID', None)
                            
                            if aid:
                                print(f"\n嘗試使用 AID {aid} 取得詳細內容...")
                                
                                # 方法2: 嘗試不同的取得內容方法
                                methods_to_try = [
                                    ('get_post with aid', lambda: ptt_bot.get_post(board=board_name, aid=aid)),
                                    ('get_article', lambda: ptt_bot.get_article(board_name, aid) if hasattr(ptt_bot, 'get_article') else None),
                                ]
                                
                                for method_name, method_func in methods_to_try:
                                    try:
                                        print(f"  嘗試 {method_name}...")
                                        result = method_func()
                                        if result:
                                            print(f"    ✅ {method_name} 成功")
                                            print(f"    結果類型: {type(result)}")
                                            
                                            # 檢查是否有推文相關資訊
                                            if isinstance(result, dict):
                                                for key in ['comments', 'pushes', 'responses', 'content']:
                                                    if key in result:
                                                        value = result[key]
                                                        print(f"    {key}: {value} (類型: {type(value)})")
                                                        if isinstance(value, list) and value:
                                                            print(f"      列表長度: {len(value)}")
                                                            print(f"      第一個元素: {value[0]}")
                                            else:
                                                for attr in ['comments', 'pushes', 'responses', 'content']:
                                                    if hasattr(result, attr):
                                                        value = getattr(result, attr)
                                                        print(f"    {attr}: {value} (類型: {type(value)})")
                                                        if isinstance(value, list) and value:
                                                            print(f"      列表長度: {len(value)}")
                                                            print(f"      第一個元素: {value[0]}")
                                        else:
                                            print(f"    ❌ {method_name} 返回空值")
                                    except Exception as e:
                                        print(f"    ❌ {method_name} 失敗: {e}")
                            
                            break  # 找到一篇文章就停止
                        else:
                            print(f"❌ 文章 {i} 不存在")
                            
                    except Exception as e:
                        print(f"❌ 取得文章 {i} 失敗: {e}")
                
                break  # 成功測試一個看板就停止
                
            except Exception as e:
                print(f"❌ 測試看板 {board_name} 失敗: {e}")
                continue
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pyptt_methods():
    """測試 PyPtt 的所有可用方法"""
    print("\n" + "=" * 60)
    print("測試 PyPtt 的所有可用方法")
    print("=" * 60)
    
    try:
        import PyPtt
        
        # 檢查 PyPtt 的所有方法
        print("PyPtt.API 的所有方法:")
        api_methods = [method for method in dir(PyPtt.API) if not method.startswith('_')]
        for method in sorted(api_methods):
            try:
                method_obj = getattr(PyPtt.API, method)
                if callable(method_obj):
                    try:
                        signature = inspect.signature(method_obj)
                        print(f"  {method}{signature}")
                    except:
                        print(f"  {method}: 無法取得簽名")
            except:
                print(f"  {method}: 無法檢查")
        
        # 檢查 PyPtt 的常數
        print("\nPyPtt 的常數和枚舉:")
        for attr_name in sorted(dir(PyPtt)):
            if not attr_name.startswith('_') and attr_name[0].isupper():
                try:
                    attr = getattr(PyPtt, attr_name)
                    print(f"  {attr_name}: {type(attr)} - {attr}")
                except:
                    print(f"  {attr_name}: 無法取得值")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("開始詳細測試 PyPtt...")
    
    # 檢查配置
    config_errors = Config.validate_config()
    if config_errors:
        print("❌ 配置錯誤:")
        for error in config_errors:
            print(f"   - {error}")
        exit(1)
    
    print(f"✅ 使用帳號: {Config.PTT_USERNAME}")
    
    # 測試 1: PyPtt 方法檢查
    success1 = test_pyptt_methods()
    
    # 測試 2: 文章結構測試
    success2 = test_pyptt_post_structure()
    
    print("\n" + "=" * 60)
    print("最終測試結果")
    print("=" * 60)
    
    if success1:
        print("✅ PyPtt 方法檢查通過")
    else:
        print("❌ PyPtt 方法檢查失敗")
    
    if success2:
        print("✅ 文章結構測試通過")
    else:
        print("❌ 文章結構測試失敗")
    
    if success1 and success2:
        print("\n🎉 PyPtt 詳細測試完成！")
    else:
        print("\n⚠️ 部分測試失敗，需要進一步檢查")
