#!/usr/bin/env python3
"""
測試簡化版圖形查詢
"""

import sys
import os
import json
from datetime import datetime

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from graph_query_simple import SimpleGraphQuery

def test_simple_graph_query():
    """測試簡化版圖形查詢"""
    print("=" * 60)
    print("測試簡化版圖形查詢")
    print("=" * 60)
    
    # 檢查配置
    config_errors = Config.validate_config()
    if config_errors:
        print("❌ 配置錯誤:")
        for error in config_errors:
            print(f"   - {error}")
        return False
    
    # 建立簡化圖形查詢物件
    graph_query = SimpleGraphQuery()
    
    try:
        if not graph_query.is_connected:
            print("❌ 無法連接到圖形資料庫")
            return False
        
        print("✅ 已連接到圖形資料庫")
        
        userid = 'felaray'
        
        # 執行關係查詢
        print(f"\n🔍 查詢 {userid} 的關係網絡...")
        result = graph_query.query_user_relationships(userid, max_depth=3)
        
        if 'error' in result:
            print(f"❌ 查詢失敗: {result['error']}")
            return False
        
        print("✅ 查詢成功！")
        
        # 顯示統計資訊
        stats = result['statistics']
        print(f"\n📊 統計資訊:")
        print(f"  總關聯數: {stats['total_connections']}")
        print(f"  節點數: {stats['node_count']}")
        print(f"  邊數: {stats['edge_count']}")
        
        # 按層顯示
        print(f"\n🔍 分層統計:")
        for layer, count in sorted(stats['layer_counts'].items()):
            layer_name = {
                1: "第一層（推文互動）",
                2: "第二層（IP共用）", 
                3: "第三層（同板發文）"
            }.get(layer, f"第{layer}層")
            print(f"  {layer_name}: {count} 個關聯")
        
        # 按類型顯示
        print(f"\n📋 關係類型:")
        for rel_type, count in stats['type_counts'].items():
            type_name = {
                'i_commented_their_post': "我推他的文",
                'they_commented_my_post': "他推我的文",
                'shared_ip': "IP共用者",
                'same_board_posting': "同板發文"
            }.get(rel_type, rel_type)
            print(f"  {type_name}: {count} 個")
        
        # 顯示關係詳情
        print(f"\n🔗 關係詳情:")
        for i, rel in enumerate(result['relationships'][:10]):  # 只顯示前10個
            target_display = f"{rel['target_userid']} ({rel['target_nickname']})" if rel['target_nickname'] else rel['target_userid']
            print(f"  {i+1}. {target_display} - {rel['role']} (強度: {rel['strength']:.1f})")
        
        if len(result['relationships']) > 10:
            print(f"  ... 還有 {len(result['relationships']) - 10} 個關聯")
        
        # 顯示網絡圖資料結構
        print(f"\n🌐 網絡圖資料:")
        print(f"  節點: {len(result['network_data']['nodes'])} 個")
        print(f"  邊: {len(result['network_data']['edges'])} 個")
        
        # 顯示部分節點
        print(f"\n📍 節點範例:")
        for i, node in enumerate(result['network_data']['nodes'][:5]):
            center_mark = " (中心)" if node.get('is_center') else ""
            print(f"  {i+1}. {node['label']} - 第{node['layer']}層{center_mark}")
        
        # 顯示部分邊
        print(f"\n🔗 邊範例:")
        for i, edge in enumerate(result['network_data']['edges'][:5]):
            print(f"  {i+1}. {edge['source']} → {edge['target']} ({edge['label']})")
        
        # 檢查是否有推文互動
        has_comment_interaction = any(
            rel['type'] in ['i_commented_their_post', 'they_commented_my_post'] 
            for rel in result['relationships']
        )
        
        if has_comment_interaction:
            print("\n🎉 成功！發現推文互動關係")
            print("這證明了分層查詢邏輯正確工作：")
            print("  userA → 推文 → 文章 ← 發文 ← userB")
            print("  userA → 發文 → 文章 ← 推文 ← userB")
        else:
            print("\n⚠️ 沒有發現推文互動關係")
        
        # 保存結果到文件（用於前端測試）
        output_file = 'graph_query_result.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"\n💾 結果已保存到 {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        graph_query.close()

if __name__ == "__main__":
    print("開始測試簡化版圖形查詢...")
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = test_simple_graph_query()
    
    print("\n" + "=" * 60)
    print("測試結果")
    print("=" * 60)
    
    if success:
        print("✅ 簡化版圖形查詢測試通過！")
        print("\n💡 系統已準備好進行前端整合")
        print("   - 關係查詢邏輯正常")
        print("   - 網絡圖資料結構完整")
        print("   - 統計資訊準確")
        print("\n🎯 下一步：")
        print("   1. 完善前端圖形化介面")
        print("   2. 實現關係強度視覺化")
        print("   3. 添加互動式操作功能")
    else:
        print("❌ 簡化版圖形查詢測試失敗")
