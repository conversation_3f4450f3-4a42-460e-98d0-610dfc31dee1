#!/usr/bin/env python3
"""
測試 PyPtt 的正確導入方式
"""

print("測試 PyPtt 導入...")

try:
    import PyPtt
    print(f"✓ import PyPtt 成功")
    print(f"  PyPtt 版本: {PyPtt.__version__}")
    print(f"  PyPtt 模組內容: {dir(PyPtt)}")
    
    # 測試不同的建立方式
    print("\n測試建立 PTT 物件...")
    
    # 方法1: PyPtt.API()
    try:
        ptt_bot = PyPtt.API()
        print("✓ PyPtt.API() 成功")
        print(f"  物件類型: {type(ptt_bot)}")
        print(f"  可用方法: {[m for m in dir(ptt_bot) if not m.startswith('_')][:10]}...")
    except Exception as e:
        print(f"✗ PyPtt.API() 失敗: {e}")
    
    # 方法2: 檢查是否有其他建立方式
    if hasattr(PyPtt, 'PTT'):
        try:
            ptt_bot2 = PyPtt.PTT()
            print("✓ PyPtt.PTT() 成功")
        except Exception as e:
            print(f"✗ PyPtt.PTT() 失敗: {e}")
    else:
        print("✗ PyPtt.PTT 不存在")
    
    # 檢查其他可能的類別
    for attr in dir(PyPtt):
        if not attr.startswith('_') and attr[0].isupper():
            print(f"  可用類別: {attr}")
    
except ImportError as e:
    print(f"✗ import PyPtt 失敗: {e}")
except Exception as e:
    print(f"✗ 其他錯誤: {e}")
