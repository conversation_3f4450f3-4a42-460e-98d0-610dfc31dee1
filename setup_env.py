#!/usr/bin/env python3
"""
PTT 自動登入系統 - 安全環境變數設定工具
此工具幫助您安全地設定環境變數，避免在程式碼中暴露敏感資訊
"""

import os
import getpass
import secrets
import string
from pathlib import Path


def generate_secret_key(length=50):
    """產生安全的隨機密鑰"""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def validate_cosmos_endpoint(endpoint):
    """驗證 Cosmos DB 端點格式"""
    if not endpoint:
        return False
    if not (endpoint.startswith('https://') or endpoint.startswith('wss://')):
        return False
    if 'cosmos.azure.com' not in endpoint:
        return False
    return True


def validate_cosmos_key(key):
    """驗證 Cosmos DB 金鑰格式"""
    if not key:
        return False
    if len(key) < 50:  # Cosmos DB 金鑰通常很長
        return False
    return True


def setup_environment():
    """設定環境變數"""
    print("🔒 PTT 自動登入系統 - 安全環境變數設定")
    print("=" * 50)
    print("此工具將幫助您安全地設定環境變數")
    print("所有敏感資訊都會儲存在 .env 檔案中，不會出現在程式碼裡")
    print()
    
    # 檢查是否已存在 .env 檔案
    env_file = Path('.env')
    if env_file.exists():
        print("⚠️  發現現有的 .env 檔案")
        overwrite = input("是否要覆蓋現有設定？(y/N): ").lower().strip()
        if overwrite != 'y':
            print("設定已取消")
            return
        print()
    
    # 收集環境變數
    env_vars = {}
    
    print("📝 PTT 帳號設定")
    print("-" * 20)
    while True:
        ptt_username = input("PTT 使用者名稱: ").strip()
        if ptt_username and len(ptt_username) >= 3:
            env_vars['PTT_USERNAME'] = ptt_username
            break
        print("❌ 使用者名稱至少需要3個字符")
    
    while True:
        ptt_password = getpass.getpass("PTT 密碼 (輸入時不會顯示): ").strip()
        if ptt_password and len(ptt_password) >= 6:
            env_vars['PTT_PASSWORD'] = ptt_password
            break
        print("❌ 密碼至少需要6個字符")
    
    print()
    print("☁️  Azure Cosmos DB 設定")
    print("-" * 25)
    print("請從 Azure Portal > Cosmos DB > 金鑰 頁面取得以下資訊")
    
    while True:
        cosmos_endpoint = input("Cosmos DB 端點 (Gremlin Endpoint): ").strip()
        if validate_cosmos_endpoint(cosmos_endpoint):
            env_vars['COSMOS_DB_ENDPOINT'] = cosmos_endpoint
            break
        print("❌ 端點格式不正確，應為 https://xxx.gremlin.cosmos.azure.com:443/")
    
    while True:
        cosmos_key = getpass.getpass("Cosmos DB 金鑰 (Primary Key，輸入時不會顯示): ").strip()
        if validate_cosmos_key(cosmos_key):
            env_vars['COSMOS_DB_KEY'] = cosmos_key
            break
        print("❌ 金鑰格式不正確或太短")
    
    # 資料庫設定
    print()
    print("🗄️  資料庫設定")
    print("-" * 15)
    
    database = input("資料庫名稱 [ptt_graph_db]: ").strip()
    env_vars['COSMOS_DB_DATABASE'] = database if database else 'ptt_graph_db'
    
    collection = input("集合名稱 [ptt_graph]: ").strip()
    env_vars['COSMOS_DB_COLLECTION'] = collection if collection else 'ptt_graph'
    
    # 其他設定
    print()
    print("⚙️  系統設定")
    print("-" * 12)
    
    # 產生安全的 SECRET_KEY
    env_vars['SECRET_KEY'] = generate_secret_key()
    print("✅ 已自動產生安全的 Flask SECRET_KEY")
    
    # 其他預設設定
    env_vars.update({
        'PTT_HOST': 'ptt.cc',
        'PTT_PORT': '23',
        'LOG_LEVEL': 'INFO',
        'TIMEZONE': 'Asia/Taipei',
        'POST_SCHEDULE': '08:00',
        'MAX_RETRY_ATTEMPTS': '3',
        'CONNECTION_TIMEOUT': '30',
        'LOGIN_TIMEOUT': '10',
        'RETRY_DELAY': '5',
        'DEFAULT_BOARDS': 'Test',
        'MAX_POSTS_PER_BOARD': '50',
        'CRAWL_INTERVAL_HOURS': '24',
        'FLASK_DEBUG': 'False',
        'FLASK_ENV': 'production'
    })
    
    # 寫入 .env 檔案
    print()
    print("💾 正在儲存設定...")
    
    try:
        with open('.env', 'w', encoding='utf-8') as f:
            f.write("# PTT 自動登入系統 - 環境變數設定\n")
            f.write("# 此檔案包含敏感資訊，請勿分享或提交到版本控制\n")
            f.write(f"# 建立時間: {os.popen('date /t').read().strip()} {os.popen('time /t').read().strip()}\n\n")
            
            f.write("# PTT 連線設定\n")
            f.write(f"PTT_USERNAME={env_vars['PTT_USERNAME']}\n")
            f.write(f"PTT_PASSWORD={env_vars['PTT_PASSWORD']}\n")
            f.write(f"PTT_HOST={env_vars['PTT_HOST']}\n")
            f.write(f"PTT_PORT={env_vars['PTT_PORT']}\n\n")
            
            f.write("# Azure Cosmos DB 設定\n")
            f.write(f"COSMOS_DB_ENDPOINT={env_vars['COSMOS_DB_ENDPOINT']}\n")
            f.write(f"COSMOS_DB_KEY={env_vars['COSMOS_DB_KEY']}\n")
            f.write(f"COSMOS_DB_DATABASE={env_vars['COSMOS_DB_DATABASE']}\n")
            f.write(f"COSMOS_DB_COLLECTION={env_vars['COSMOS_DB_COLLECTION']}\n\n")
            
            f.write("# 系統設定\n")
            f.write(f"SECRET_KEY={env_vars['SECRET_KEY']}\n")
            f.write(f"LOG_LEVEL={env_vars['LOG_LEVEL']}\n")
            f.write(f"TIMEZONE={env_vars['TIMEZONE']}\n")
            f.write(f"POST_SCHEDULE={env_vars['POST_SCHEDULE']}\n\n")
            
            f.write("# 效能設定\n")
            f.write(f"MAX_RETRY_ATTEMPTS={env_vars['MAX_RETRY_ATTEMPTS']}\n")
            f.write(f"CONNECTION_TIMEOUT={env_vars['CONNECTION_TIMEOUT']}\n")
            f.write(f"LOGIN_TIMEOUT={env_vars['LOGIN_TIMEOUT']}\n")
            f.write(f"RETRY_DELAY={env_vars['RETRY_DELAY']}\n\n")
            
            f.write("# 爬蟲設定\n")
            f.write(f"DEFAULT_BOARDS={env_vars['DEFAULT_BOARDS']}\n")
            f.write(f"MAX_POSTS_PER_BOARD={env_vars['MAX_POSTS_PER_BOARD']}\n")
            f.write(f"CRAWL_INTERVAL_HOURS={env_vars['CRAWL_INTERVAL_HOURS']}\n\n")
            
            f.write("# Flask 設定\n")
            f.write(f"FLASK_DEBUG={env_vars['FLASK_DEBUG']}\n")
            f.write(f"FLASK_ENV={env_vars['FLASK_ENV']}\n")
        
        print("✅ 環境變數設定完成！")
        print()
        print("🔒 安全提醒:")
        print("   • .env 檔案已建立，包含您的敏感資訊")
        print("   • 此檔案已在 .gitignore 中，不會被提交到版本控制")
        print("   • 請勿將此檔案分享給他人")
        print("   • 定期更換密碼和金鑰")
        print()
        print("🚀 下一步:")
        print("   1. 重新啟動應用程式: py app.py")
        print("   2. 開啟瀏覽器檢查系統配置狀態")
        print("   3. 如需部署到 Azure，請參考 SECURITY_SETUP.md")
        
    except Exception as e:
        print(f"❌ 儲存設定時發生錯誤: {e}")
        return
    
    print()
    print("設定完成！請重新啟動應用程式以載入新的環境變數。")


if __name__ == "__main__":
    try:
        setup_environment()
    except KeyboardInterrupt:
        print("\n\n設定已取消")
    except Exception as e:
        print(f"\n❌ 發生錯誤: {e}")
        print("請檢查您的輸入並重試")
