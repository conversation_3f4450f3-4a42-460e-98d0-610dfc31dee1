import os
from dotenv import load_dotenv
from gremlin_python.driver import client, serializer

# 讀取 .env 檔案
load_dotenv()

# 從環境變數讀取設定
COSMOS_DB_ENDPOINT = os.getenv("COSMOS_DB_ENDPOINT")
COSMOS_DB_KEY = os.getenv("COSMOS_DB_KEY")
COSMOS_DB_DATABASE = os.getenv("COSMOS_DB_DATABASE")
COSMOS_DB_COLLECTION = os.getenv("COSMOS_DB_COLLECTION")

print("=== Cosmos DB Gremlin 連接測試 ===")
print(f"端點: {COSMOS_DB_ENDPOINT}")
print(f"資料庫: {COSMOS_DB_DATABASE}")
print(f"集合: {COSMOS_DB_COLLECTION}")
print(f"金鑰已設定: {'是' if COSMOS_DB_KEY else '否'}")

# 建立 Gremlin 用戶端
gremlin_client = None

try:
    print("\n1. 嘗試使用 aiohttp 傳輸建立客戶端...")
    from gremlin_python.driver.aiohttp.transport import AiohttpTransport
    transport_factory = lambda: AiohttpTransport()

    gremlin_client = client.Client(
        COSMOS_DB_ENDPOINT,
        'g',
        username=f"/dbs/{COSMOS_DB_DATABASE}/colls/{COSMOS_DB_COLLECTION}",
        password=COSMOS_DB_KEY,
        message_serializer=serializer.GraphSONSerializersV2d0(),
        transport_factory=transport_factory
    )
    print("✓ 使用 aiohttp 傳輸建立客戶端成功")

except ImportError:
    print("⚠️ 無法匯入 AiohttpTransport，使用預設傳輸")
    try:
        gremlin_client = client.Client(
            COSMOS_DB_ENDPOINT,
            'g',
            username=f"/dbs/{COSMOS_DB_DATABASE}/colls/{COSMOS_DB_COLLECTION}",
            password=COSMOS_DB_KEY,
            message_serializer=serializer.GraphSONSerializersV2d0()
        )
        print("✓ 使用預設傳輸建立客戶端成功")
    except Exception as e:
        print(f"❌ 建立客戶端失敗: {e}")
        gremlin_client = None

except Exception as e:
    print(f"❌ 建立客戶端失敗: {e}")
    gremlin_client = None

# 測試連線與查詢
def test_connection():
    if not gremlin_client:
        print("❌ 客戶端未建立")
        return False

    try:
        print("\n2. 測試基本查詢...")
        # 使用同步方式查詢
        result = gremlin_client.submit("g.V().count()").all().result()
        print(f"✅ 成功連接 Cosmos DB，目前頂點數量：{result[0] if result else 0}")

        print("\n3. 測試寫入頂點...")
        # 測試寫入一個簡單的頂點
        write_query = """
        g.addV('test')
        .property('name', 'test_connection')
        .property('partitionKey', 'test')
        .property('timestamp', '2025-06-19T13:00:00')
        """

        write_result = gremlin_client.submit(write_query).all().result()
        print(f"✅ 頂點寫入成功：{len(write_result)} 個頂點")

        print("\n4. 驗證寫入結果...")
        # 查詢剛寫入的頂點
        verify_result = gremlin_client.submit("g.V().hasLabel('test').values('name')").all().result()
        print(f"✅ 驗證成功，找到測試頂點：{verify_result}")

        print("\n5. 清理測試資料...")
        # 清理測試資料
        gremlin_client.submit("g.V().hasLabel('test').drop()").all().result()
        print("✅ 測試資料已清理")

        return True

    except Exception as e:
        print(f"❌ 測試失敗：{e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 確保關閉連接
        try:
            if gremlin_client:
                gremlin_client.close()
                print("\n✅ 連接已關閉")
        except Exception as e:
            print(f"⚠️ 關閉連接時發生錯誤：{e}")

if __name__ == "__main__":
    success = test_connection()
    if success:
        print("\n🎉 所有測試通過！Cosmos DB 連接和寫入功能正常。")
        exit(0)
    else:
        print("\n❌ 測試失敗，請檢查配置和網路連接。")
        exit(1)