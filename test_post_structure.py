#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 PTT 文章物件結構
"""

import sys
import PyPtt
from config import Config

# 設定正確的編碼
sys.stdout.reconfigure(encoding='utf-8')

def test_post_structure():
    """測試文章物件結構"""
    print("=" * 60)
    print("PTT 文章物件結構測試")
    print("=" * 60)
    
    try:
        # 建立 PTT API 並登入
        ptt_bot = PyPtt.API()
        ptt_bot.login(
            ptt_id=Config.PTT_USERNAME,
            ptt_pw=Config.PTT_PASSWORD,
            kick_other_session=True
        )
        print("✅ PTT 登入成功")
        
        # 取得看板最新索引
        board_name = "Test"
        newest_index = ptt_bot.get_newest_index(
            index_type=PyPtt.NewIndex.BOARD,
            board=board_name
        )
        print(f"✅ 最新索引: {newest_index}")
        
        # 取得文章並檢查結構
        for i in range(max(1, newest_index - 2), newest_index + 1):
            print(f"\n--- 文章 {i} ---")
            try:
                post = ptt_bot.get_post(
                    board=board_name,
                    index=i
                )
                
                if post:
                    print(f"文章物件類型: {type(post)}")
                    print(f"文章物件屬性: {dir(post)}")
                    
                    # 檢查常見屬性
                    attrs_to_check = [
                        'title', 'author', 'date', 'aid', 'id', 'content',
                        'ip', 'board', 'index', 'push_count', 'money'
                    ]
                    
                    print("文章屬性值:")
                    for attr in attrs_to_check:
                        if hasattr(post, attr):
                            value = getattr(post, attr)
                            print(f"  {attr}: {value} (類型: {type(value)})")
                    
                    # 如果是字典類型
                    if isinstance(post, dict):
                        print("文章字典內容:")
                        for key, value in post.items():
                            print(f"  {key}: {value}")
                    
                    break  # 只檢查第一篇找到的文章
                else:
                    print(f"文章 {i} 不存在或無法取得")
                    
            except Exception as e:
                print(f"取得文章 {i} 失敗: {e}")
        
        # 登出
        ptt_bot.logout()
        print("\n✅ PTT 登出成功")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

if __name__ == "__main__":
    test_post_structure()
