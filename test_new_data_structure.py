#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試新的資料結構 - 分離 userid 和 nickname
"""

import sys
from graph_writer import GraphWriter

# 設定正確的編碼
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')

def test_new_data_structure():
    """測試新的資料結構"""
    print("=" * 60)
    print("測試新的資料結構 - 分離 userid 和 nickname")
    print("=" * 60)
    
    try:
        # 建立圖形寫入器
        writer = GraphWriter()
        
        # 建立測試資料 - 使用新的資料結構
        test_graph_data = {
            'vertices': {
                'users': [
                    {
                        'id': 'testuser001 (測試使用者一)',  # 這會被解析為 userid 和 nickname
                        'properties': {
                            'first_seen': '2025-06-19T10:00:00',
                            'last_seen': '2025-06-19T10:30:00'
                        }
                    },
                    {
                        'id': 'testuser002 (測試使用者二)',
                        'properties': {
                            'first_seen': '2025-06-19T10:15:00',
                            'last_seen': '2025-06-19T10:45:00'
                        }
                    },
                    {
                        'id': 'simpleuser',  # 沒有暱稱的使用者
                        'properties': {
                            'first_seen': '2025-06-19T11:00:00',
                            'last_seen': '2025-06-19T11:30:00'
                        }
                    }
                ],
                'ips': [
                    {
                        'id': '*************',
                        'properties': {
                            'first_seen': '2025-06-19T10:00:00',
                            'last_seen': '2025-06-19T11:30:00'
                        }
                    },
                    {
                        'id': '*************',
                        'properties': {
                            'first_seen': '2025-06-19T10:15:00',
                            'last_seen': '2025-06-19T10:45:00'
                        }
                    }
                ],
                'posts': [
                    {
                        'id': 'Test.NewStructure001',
                        'properties': {
                            'title': '測試新資料結構的文章',
                            'board': 'Test',
                            'date': '2025-06-19T10:30:00',
                            'content': '這是測試新資料結構的文章內容'
                        }
                    },
                    {
                        'id': 'Test.NewStructure002',
                        'properties': {
                            'title': '第二篇測試文章',
                            'board': 'Test',
                            'date': '2025-06-19T10:45:00',
                            'content': '這是第二篇測試文章'
                        }
                    }
                ]
            },
            'edges': {
                'posted': [
                    {
                        'from': 'testuser001 (測試使用者一)',
                        'to': 'Test.NewStructure001',
                        'properties': {
                            'date': '2025-06-19T10:30:00',
                            'board': 'Test'
                        }
                    },
                    {
                        'from': 'testuser002 (測試使用者二)',
                        'to': 'Test.NewStructure002',
                        'properties': {
                            'date': '2025-06-19T10:45:00',
                            'board': 'Test'
                        }
                    }
                ],
                'used_ip': [
                    {
                        'from': 'testuser001 (測試使用者一)',
                        'to': '*************',
                        'properties': {
                            'date': '2025-06-19T10:30:00',
                            'action': 'posted'
                        }
                    },
                    {
                        'from': 'testuser002 (測試使用者二)',
                        'to': '*************',  # 相同 IP
                        'properties': {
                            'date': '2025-06-19T10:45:00',
                            'action': 'posted'
                        }
                    },
                    {
                        'from': 'simpleuser',
                        'to': '*************',
                        'properties': {
                            'date': '2025-06-19T11:00:00',
                            'action': 'browsed'
                        }
                    }
                ],
                'from_ip': [
                    {
                        'from': 'Test.NewStructure001',
                        'to': '*************',
                        'properties': {
                            'date': '2025-06-19T10:30:00'
                        }
                    },
                    {
                        'from': 'Test.NewStructure002',
                        'to': '*************',
                        'properties': {
                            'date': '2025-06-19T10:45:00'
                        }
                    }
                ],
                'commented': []  # 暫時沒有推文測試資料
            }
        }
        
        print("建立的測試資料:")
        print(f"  使用者: {len(test_graph_data['vertices']['users'])} 個")
        print(f"  IP: {len(test_graph_data['vertices']['ips'])} 個")
        print(f"  文章: {len(test_graph_data['vertices']['posts'])} 篇")
        print(f"  發文關聯: {len(test_graph_data['edges']['posted'])} 條")
        print(f"  IP 使用關聯: {len(test_graph_data['edges']['used_ip'])} 條")
        
        # 顯示使用者名稱解析
        print("\n使用者名稱解析測試:")
        for user in test_graph_data['vertices']['users']:
            userid, nickname = writer._parse_username(user['id'])
            print(f"  原始: '{user['id']}' -> userid: '{userid}', nickname: '{nickname}'")
        
        # 寫入資料
        print("\n開始寫入測試資料...")
        result = writer.write_graph_data(test_graph_data)
        
        if result:
            print("✅ 測試資料寫入成功！")
        else:
            print("❌ 測試資料寫入失敗")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_query_new_structure():
    """測試查詢新的資料結構"""
    print("\n" + "=" * 60)
    print("測試查詢新的資料結構")
    print("=" * 60)
    
    try:
        from graph_query import GraphQuery
        
        # 建立查詢器
        query = GraphQuery()
        
        # 測試使用者查詢 - 使用完整名稱
        print("\n1. 測試使用者查詢 (完整名稱):")
        result1 = query.analyze_user_ip_relationships('testuser001 (測試使用者一)')
        
        if 'error' not in result1:
            print(f"✅ 查詢成功")
            print(f"   使用者: {result1['username']}")
            print(f"   userid: {result1['userid']}")
            print(f"   nickname: {result1['nickname']}")
            print(f"   使用的 IP 數: {len(result1['user_ips'])}")
            print(f"   相關使用者數: {len(result1['related_users'])}")
            
            if result1['related_users']:
                print("   相關使用者:")
                for related in result1['related_users']:
                    print(f"     - {related['username']} (共享 {len(related['shared_ips'])} 個 IP)")
        else:
            print(f"❌ 查詢失敗: {result1['error']}")
        
        # 測試使用者查詢 - 只使用 userid
        print("\n2. 測試使用者查詢 (只用 userid):")
        result2 = query.analyze_user_ip_relationships('testuser002')
        
        if 'error' not in result2:
            print(f"✅ 查詢成功")
            print(f"   使用者: {result2['username']}")
            print(f"   userid: {result2['userid']}")
            print(f"   nickname: {result2['nickname']}")
        else:
            print(f"❌ 查詢失敗: {result2['error']}")
        
        # 測試使用者列表
        print("\n3. 測試使用者列表:")
        users = query.get_all_users(limit=10)
        
        print(f"找到 {len(users)} 個使用者:")
        for user in users:
            userid = user.get('userid', '未知')
            nickname = user.get('nickname', '')
            display_name = f"{userid} ({nickname})" if nickname else userid
            print(f"   - {display_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 查詢測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 測試新資料結構的寫入
    write_success = test_new_data_structure()
    
    if write_success:
        # 測試新資料結構的查詢
        query_success = test_query_new_structure()
        
        if query_success:
            print("\n🎉 新資料結構測試完全成功！")
            print("\n主要改進:")
            print("✅ userid 和 nickname 分離儲存")
            print("✅ 查詢支援完整名稱和 userid")
            print("✅ 資料結構更清晰和靈活")
        else:
            print("\n⚠️ 寫入成功但查詢有問題")
    else:
        print("\n❌ 新資料結構測試失敗")
