from gremlin_python.driver import client, serializer
from config import Config

# 使用 config 中的連線設定
GREMLIN_ENDPOINT = Config.COSMOS_DB_ENDPOINT
GREMLIN_KEY = Config.COSMOS_DB_KEY
DATABASE = Config.COSMOS_DB_DATABASE
GRAPH = Config.COSMOS_DB_COLLECTION

# 確保端點格式正確
endpoint = GREMLIN_ENDPOINT
if not endpoint.endswith('/'):
    endpoint += '/'

gclient = client.Client(
    endpoint,
    'g',
    username=f"/dbs/{DATABASE}/colls/{GRAPH}",
    password=GREMLIN_KEY,
    message_serializer=serializer.GraphSONSerializersV2d0()
)

def submit(query):
    return list(gclient.submit(query).all().result())

# --- 建立 Vertex (加入 partitionKey 屬性) ---
submit("g.addV('user').property('id', 'u-alice').property('username', 'alice').property('partitionKey', 'user')")
submit("g.addV('ip').property('id', 'ip-*******').property('address', '*******').property('partitionKey', 'ip')")
submit("g.addV('post').property('id', 'a-1001').property('title', 'Hello World').property('board', 'Gossiping').property('partitionKey', 'post')")
submit("g.addV('comment').property('id', 'c-2001').property('content', '推！').property('board', 'Gossiping').property('partitionKey', 'comment')")

# --- 建立 Edge ---
submit("g.V('u-alice').addE('USED_IP').to(g.V('ip-*******')).property('action_type', 'login')")
submit("g.V('u-alice').addE('POSTED').to(g.V('a-1001'))")
submit("g.V('a-1001').addE('FROM_IP').to(g.V('ip-*******'))")
submit("g.V('u-alice').addE('COMMENTED').to(g.V('c-2001'))")
submit("g.V('c-2001').addE('FROM_IP').to(g.V('ip-*******'))")
submit("g.V('c-2001').addE('IN_ARTICLE').to(g.V('a-1001'))")

# --- 查詢: 該 IP 被誰用過 ---
result = submit("""
g.V().hasLabel('ip').has('address', '*******')
  .in('USED_IP')
  .values('username')
""")
print("使用該 IP 的帳號：", result)

# --- 查詢: 該 IP 發了哪些文章 ---
result = submit("""
g.V().hasLabel('ip').has('address', '*******')
  .in('FROM_IP')
  .hasLabel('post')
  .valueMap()
""")
print("該 IP 發的文章：", result)
