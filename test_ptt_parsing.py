#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 PTT 文章解析功能
"""

import sys
from ptt_client import PTTClient

# 設定正確的編碼
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')

def test_ptt_parsing():
    """測試 PTT 文章解析"""
    print("=" * 60)
    print("測試 PTT 文章解析功能")
    print("=" * 60)
    
    try:
        # 建立 PTT 客戶端
        client = PTTClient()
        
        # 登入 PTT
        print("1. 登入 PTT...")
        if not client.login():
            print("❌ PTT 登入失敗")
            return False
        
        print("✅ PTT 登入成功")
        
        # 測試取得看板文章列表
        print("\n2. 測試取得看板文章列表...")
        board_name = "Test"
        posts = client.get_board_posts(board_name, max_posts=3)
        
        if not posts:
            print("❌ 無法取得文章列表")
            return False
        
        print(f"✅ 成功取得 {len(posts)} 篇文章")
        
        # 顯示文章基本資訊
        print("\n文章基本資訊:")
        for i, post in enumerate(posts):
            print(f"  文章 {i+1}:")
            print(f"    AID: {post.get('aid', 'N/A')}")
            print(f"    標題: '{post.get('title', 'N/A')}'")
            print(f"    作者: '{post.get('author', 'N/A')}'")
            print(f"    日期: '{post.get('date', 'N/A')}'")
            print(f"    IP: {post.get('ip', 'N/A')}")
            print()
        
        # 測試取得文章詳細內容
        if posts and posts[0].get('aid'):
            print("3. 測試取得文章詳細內容...")
            first_post = posts[0]
            aid = first_post['aid']
            
            print(f"正在取得文章 {aid} 的詳細內容...")
            detailed_content = client.get_post_content(aid, board_name)
            
            if detailed_content:
                print("✅ 成功取得文章詳細內容")
                print(f"  標題: '{detailed_content.get('title', 'N/A')}'")
                print(f"  作者: '{detailed_content.get('author', 'N/A')}'")
                print(f"  日期: '{detailed_content.get('date', 'N/A')}'")
                print(f"  IP: {detailed_content.get('ip', 'N/A')}")
                print(f"  內容長度: {len(detailed_content.get('content', ''))}")
                print(f"  推文數: {len(detailed_content.get('comments', []))}")
                
                # 顯示內容前 200 字元
                content = detailed_content.get('content', '')
                if content:
                    print(f"  內容預覽: {content[:200]}...")
                else:
                    print("  內容: (空)")
                    
            else:
                print("❌ 無法取得文章詳細內容")
        
        # 測試原始 PyPtt API
        print("\n4. 測試原始 PyPtt API...")
        try:
            # 直接使用 PyPtt 的 get_newest_index
            newest_index = client.ptt_bot.get_newest_index(board_name)
            print(f"✅ 最新文章索引: {newest_index}")
            
            # 嘗試取得文章列表
            if hasattr(client.ptt_bot, 'get_post'):
                print("  PyPtt 有 get_post 方法")
            else:
                print("  PyPtt 沒有 get_post 方法")
                
            if hasattr(client.ptt_bot, 'get_article'):
                print("  PyPtt 有 get_article 方法")
            else:
                print("  PyPtt 沒有 get_article 方法")
                
            # 嘗試直接取得文章
            if newest_index and newest_index > 0:
                print(f"  嘗試取得索引 {newest_index} 的文章...")
                try:
                    post_info = client.ptt_bot.get_post(board_name, index=newest_index)
                    if post_info:
                        print(f"  ✅ 成功取得文章")
                        print(f"    標題: '{getattr(post_info, 'title', 'N/A')}'")
                        print(f"    作者: '{getattr(post_info, 'author', 'N/A')}'")
                        print(f"    日期: '{getattr(post_info, 'date', 'N/A')}'")
                        print(f"    AID: '{getattr(post_info, 'aid', 'N/A')}'")
                    else:
                        print("  ❌ 取得的文章為空")
                except Exception as e:
                    print(f"  ❌ 取得文章失敗: {e}")
                    
        except Exception as e:
            print(f"❌ 測試原始 PyPtt API 失敗: {e}")
        
        # 登出
        print("\n5. 登出 PTT...")
        client.logout()
        print("✅ PTT 登出成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_pyptt():
    """直接測試 PyPtt 功能"""
    print("\n" + "=" * 60)
    print("直接測試 PyPtt 功能")
    print("=" * 60)
    
    try:
        import PyPtt
        from config import Config
        
        # 建立 PyPtt 物件
        ptt_bot = PyPtt.API()
        
        print("1. 登入 PTT...")
        try:
            ptt_bot.login(
                Config.PTT_USERNAME,
                Config.PTT_PASSWORD,
                kick_other_session=True
            )
            print("✅ 登入成功")
        except Exception as e:
            print(f"❌ 登入失敗: {e}")
            return False
        
        # 測試取得看板資訊
        print("\n2. 測試取得看板資訊...")
        try:
            board_info = ptt_bot.get_board_info("Test")
            print(f"✅ 看板資訊: {board_info}")
        except Exception as e:
            print(f"❌ 取得看板資訊失敗: {e}")
        
        # 測試取得最新索引
        print("\n3. 測試取得最新索引...")
        try:
            newest_index = ptt_bot.get_newest_index("Test")
            print(f"✅ 最新索引: {newest_index}")
            
            if newest_index and newest_index > 0:
                # 測試取得文章
                print(f"\n4. 測試取得文章 (索引 {newest_index})...")
                try:
                    post = ptt_bot.get_post("Test", index=newest_index)
                    if post:
                        print("✅ 成功取得文章")
                        print(f"  標題: '{post.title}'")
                        print(f"  作者: '{post.author}'")
                        print(f"  日期: '{post.date}'")
                        print(f"  AID: '{post.aid}'")
                        print(f"  內容長度: {len(post.content) if post.content else 0}")
                        
                        # 顯示文章內容前 100 字元
                        if post.content:
                            print(f"  內容預覽: {post.content[:100]}...")
                        
                        # 顯示推文
                        if hasattr(post, 'comments') and post.comments:
                            print(f"  推文數: {len(post.comments)}")
                            for i, comment in enumerate(post.comments[:3]):
                                print(f"    推文 {i+1}: {comment.type} {comment.author}: {comment.content}")
                        else:
                            print("  推文數: 0")
                            
                    else:
                        print("❌ 取得的文章為空")
                        
                except Exception as e:
                    print(f"❌ 取得文章失敗: {e}")
                    import traceback
                    traceback.print_exc()
                    
        except Exception as e:
            print(f"❌ 取得最新索引失敗: {e}")
        
        # 登出
        print("\n5. 登出...")
        try:
            ptt_bot.logout()
            print("✅ 登出成功")
        except Exception as e:
            print(f"❌ 登出失敗: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接測試 PyPtt 失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 測試我們的 PTT 客戶端
    success1 = test_ptt_parsing()
    
    # 直接測試 PyPtt
    success2 = test_direct_pyptt()
    
    if success1 and success2:
        print("\n🎉 PTT 解析測試完全成功！")
    elif success1:
        print("\n⚠️ PTT 客戶端測試成功，但直接 PyPtt 測試有問題")
    elif success2:
        print("\n⚠️ 直接 PyPtt 測試成功，但 PTT 客戶端有問題")
    else:
        print("\n❌ PTT 解析測試失敗")
