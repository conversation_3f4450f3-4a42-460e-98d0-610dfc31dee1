#!/usr/bin/env python3
"""
簡單的 Cosmos DB 寫入測試
"""

from config import Config
from gremlin_python.driver import client, serializer
from gremlin_python.driver.aiohttp.transport import AiohttpTransport

def test_simple_write():
    """測試簡單的頂點寫入"""
    print("=== 簡單 Cosmos DB 寫入測試 ===")
    
    try:
        # 建立客戶端
        transport_factory = lambda: AiohttpTransport()
        
        gremlin_client = client.Client(
            Config.COSMOS_DB_ENDPOINT,
            'g',
            username=f"/dbs/{Config.COSMOS_DB_DATABASE}/colls/{Config.COSMOS_DB_COLLECTION}",
            password=Config.COSMOS_DB_KEY,
            message_serializer=serializer.GraphSONSerializersV2d0(),
            transport_factory=transport_factory
        )
        
        print("✓ 客戶端建立成功")
        
        # 測試簡單查詢
        result = gremlin_client.submit("g.V().count()").all().result()
        print(f"✓ 目前頂點數量: {result[0] if result else 0}")
        
        # 測試寫入一個簡單的頂點
        print("\n嘗試寫入測試頂點...")
        query = """
        g.addV('test')
        .property('name', 'test_vertex')
        .property('partitionKey', 'test')
        .property('created_at', '2025-06-19T13:00:00')
        """
        
        result = gremlin_client.submit(query).all().result()
        print(f"✓ 頂點寫入成功: {result}")
        
        # 再次查詢頂點數量
        result = gremlin_client.submit("g.V().count()").all().result()
        print(f"✓ 寫入後頂點數量: {result[0] if result else 0}")
        
        # 查詢剛寫入的頂點
        result = gremlin_client.submit("g.V().hasLabel('test').values('name')").all().result()
        print(f"✓ 測試頂點名稱: {result}")
        
        # 清理測試資料
        print("\n清理測試資料...")
        gremlin_client.submit("g.V().hasLabel('test').drop()").all().result()
        print("✓ 測試資料已清理")
        
        gremlin_client.close()
        print("\n🎉 簡單寫入測試成功！")
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

if __name__ == "__main__":
    success = test_simple_write()
    exit(0 if success else 1)
