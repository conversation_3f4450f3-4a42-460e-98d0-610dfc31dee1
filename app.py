"""
PTT 自動登入系統 - 主應用程式
"""
import os
from flask import Flask, render_template, request, jsonify, send_from_directory, abort
from config import Config
from ptt_client import PTTClient
from logger import ptt_logger

# 建立 Flask 應用程式，預設靜態檔案目錄為 Vue 編譯輸出
app = Flask(__name__, static_folder='static/dist', static_url_path='/')
app.config.from_object(Config)

# 設定 JSON 編碼，確保中文字符正確顯示
app.config['JSON_AS_ASCII'] = False
app.config['JSONIFY_PRETTYPRINT_REGULAR'] = True

# 註冊 API Blueprint
from api import api_bp
app.register_blueprint(api_bp)

# 建立 PTT 客戶端實例
ptt_client = PTTClient()

@app.route('/')
def index():
    """SPA 首頁，如無編譯檔則回退舊模板"""
    vue_index = os.path.join(app.static_folder, 'index.html')
    if os.path.exists(vue_index):
        return send_from_directory(app.static_folder, 'index.html')

    ptt_logger.info('收到首頁請求')

    # 取得配置摘要
    config_summary = Config.get_config_summary()

    # 驗證配置
    config_errors = Config.validate_config()

    return render_template('index.html',
                         config=config_summary,
                         config_errors=config_errors)

@app.route('/favicon.ico')
def favicon():
    """網站圖示"""
    return send_from_directory(os.path.join(app.root_path, 'static'),
                               'favicon.ico', mimetype='image/vnd.microsoft.icon')

@app.route('/test-login', methods=['POST'])
def test_login():
    """測試 PTT 登入功能"""
    ptt_logger.info('收到 PTT 登入測試請求')

    try:
        # 取得表單資料
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()

        # 如果沒有提供帳密，使用環境變數
        if not username:
            username = Config.PTT_USERNAME
        if not password:
            password = Config.PTT_PASSWORD

        # 驗證必要參數
        if not username or not password:
            return jsonify({
                'success': False,
                'message': '請提供 PTT 帳號和密碼',
                'details': ['帳號或密碼為空']
            })

        # 執行登入測試
        result = ptt_client.test_login(username, password)

        ptt_logger.info(f'PTT 登入測試完成 - 結果: {result["success"]}')
        return jsonify(result)

    except Exception as e:
        error_msg = f'測試過程發生錯誤: {str(e)}'
        ptt_logger.error(error_msg)
        return jsonify({
            'success': False,
            'message': error_msg,
            'details': [str(e)]
        })

@app.route('/status')
def status():
    """取得系統狀態"""
    try:
        ptt_status = ptt_client.get_status()
        config_errors = Config.validate_config()

        return jsonify({
            'ptt_client': ptt_status,
            'config_valid': len(config_errors) == 0,
            'config_errors': config_errors
        })
    except Exception as e:
        return jsonify({
            'error': str(e)
        })

@app.route('/health')
def health():
    """健康檢查端點"""
    return jsonify({
        'status': 'healthy',
        'service': 'PTT Auto Login System'
    })

@app.route('/user-analysis')
def user_analysis():
    """使用者關聯分析頁面"""
    return render_template('user_analysis.html')

@app.route('/ip-analysis')
def ip_analysis():
    """IP 分析頁面"""
    return render_template('ip_analysis.html')

@app.route('/post-analysis')
def post_analysis():
    """文章互動分析頁面"""
    return render_template('post_analysis.html')

@app.route('/system-status')
def system_status():
    """系統狀態頁面"""
    return render_template('system_status.html')

@app.route('/crawl-management')
def crawl_management():
    """爬文管理頁面"""
    return render_template('crawl_management.html')

# Vue SPA 入口的子路由處理
@app.route('/<path:path>')
def spa_catch_all(path):
    """將未知路由導向 Vue 編譯結果"""
    file_path = os.path.join(app.static_folder, path)
    if os.path.exists(file_path):
        return send_from_directory(app.static_folder, path)
    vue_index = os.path.join(app.static_folder, 'index.html')
    if os.path.exists(vue_index):
        return send_from_directory(app.static_folder, 'index.html')
    abort(404)

@app.errorhandler(404)
def not_found(error):
    """404 錯誤處理，SPA 路由回退"""
    vue_index = os.path.join(app.static_folder, 'index.html')
    if os.path.exists(vue_index):
        return send_from_directory(app.static_folder, 'index.html'), 200
    return render_template('index.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """500 錯誤處理"""
    ptt_logger.error(f'內部伺服器錯誤: {error}')
    return jsonify({
        'success': False,
        'message': '內部伺服器錯誤'
    }), 500

if __name__ == '__main__':
    ptt_logger.info('PTT 自動登入系統啟動')
    ptt_logger.info(f'配置摘要: {Config.get_config_summary()}')

    # 檢查配置
    config_errors = Config.validate_config()
    if config_errors:
        ptt_logger.warning('配置檢查發現問題:')
        for error in config_errors:
            ptt_logger.warning(f'  - {error}')

    app.run(debug=Config.DEBUG)
