# 🧩 PTT 人物關係圖 - Graph DB 設計（含 Azure Cosmos DB 查詢範例）

## 節點定義（Vertices）

| Label     | Properties                               | 說明                         |
|-----------|------------------------------------------|------------------------------|
| `User`    | `userId`, `username`, `nick`, `karma`, `lastSeen` | 使用者帳號資訊               |
| `Post`    | `postId`, `board`, `title`, `timestamp`  | 一篇發文                     |
| `Comment` | `commentId`, `content`, `timestamp`      | 一則留言                     |
| `IP`      | `ipAddress`                              | 使用者登入過的 IP 地址       |

## 邊定義（Edges）

| Edge Label        | From → To          | 說明                                         |
|-------------------|--------------------|----------------------------------------------|
| `POSTED`          | `User → Post`      | 使用者發文                                   |
| `COMMENTED`       | `User → Comment`   | 使用者寫了一則留言                           |
| `REPLIED_TO`      | `Comment → Comment`| 留言回覆另一則留言                           |
| `ON_POST`         | `Comment → Post`   | 留言所屬的文章                               |
| `SAME_IP`         | `User → IP`        | 使用者登入過的 IP                            |
| `MENTIONED`       | `User → User`      | 使用者留言中提及另一位使用者（例如推文@）    |
| `INTERACTED_WITH` | `User → User`      | 在同一篇文章下出現互動（回文、推文等）       |
| `REPOSTED`        | `User → Post`      | 使用者轉貼他人文章（可選）                   |

---

## 🔍 Azure Cosmos DB (Gremlin API) 查詢範例

### 1️⃣ 查詢某用戶的常互動使用者
```gremlin
g.V().hasLabel('User').has('userId', 'user123')
  .out('COMMENTED')               // 該使用者發過的留言
  .out('ON_POST')                 // 該留言所在的文章
  .in('ON_POST')                  // 同篇文章的其他留言
  .in('COMMENTED')                // 這些留言的發言人
  .where(neq('user123'))          // 排除自己
  .groupCount()
  .order(local).by(values, desc)
```

### 2️⃣ 查詢共用相同 IP 的帳號
```gremlin
g.V().has('User', 'userId', 'user123')
  .out('SAME_IP')                 // 該使用者用過的 IP
  .in('SAME_IP')                  // 其他使用同一 IP 的帳號
  .dedup()
  .where(neq('user123'))
```

### 3️⃣ 查詢某篇文章所有留言與留言者
```gremlin
g.V().has('Post', 'postId', 'P123')
  .in('ON_POST')                  // 所有在這篇文章的留言
  .as('comment')
  .in('COMMENTED')                // 各留言的使用者
  .as('user')
  .select('user', 'comment')
```

### 4️⃣ 查詢某留言的回覆留言串
```gremlin
g.V().has('Comment', 'commentId', 'C123')
  .in('REPLIED_TO')               // 回覆該留言的留言們
```