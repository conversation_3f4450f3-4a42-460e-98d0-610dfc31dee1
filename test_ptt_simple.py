#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡單的 PTT 測試 - 解決編碼問題
"""

import sys
import os
import PyPtt
from config import Config

# 設定正確的編碼
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

def test_ptt_simple():
    """簡單的 PTT 測試"""
    print("=" * 60)
    print("PTT 簡單測試 - 解決編碼問題")
    print("=" * 60)
    
    try:
        # 1. 建立 PTT API
        print("1. 建立 PTT API...")
        ptt_bot = PyPtt.API()
        print("✅ PTT API 建立成功")
        
        # 2. 登入
        print(f"2. 登入 PTT - 使用者: {Config.PTT_USERNAME}")
        ptt_bot.login(
            ptt_id=Config.PTT_USERNAME,
            ptt_pw=Config.PTT_PASSWORD,
            kick_other_session=True
        )
        print("✅ PTT 登入成功")
        
        # 3. 測試取得看板最新索引
        board_name = "Test"
        print(f"3. 測試取得 {board_name} 看板最新索引...")
        try:
            newest_index = ptt_bot.get_newest_index(
                index_type=PyPtt.NewIndex.BOARD,
                board=board_name
            )
            print(f"✅ 最新索引: {newest_index}")
        except Exception as e:
            print(f"❌ 取得索引失敗: {e}")
            return False
        
        # 4. 測試取得單篇文章
        if newest_index > 0:
            print(f"4. 測試取得文章 {newest_index}...")
            try:
                post = ptt_bot.get_post(
                    board=board_name,
                    index=newest_index
                )
                if post:
                    print(f"✅ 文章標題: {getattr(post, 'title', 'N/A')}")
                    print(f"✅ 文章作者: {getattr(post, 'author', 'N/A')}")
                    print(f"✅ 文章日期: {getattr(post, 'date', 'N/A')}")
                else:
                    print("❌ 無法取得文章")
            except Exception as e:
                print(f"❌ 取得文章失敗: {e}")
        
        # 5. 測試取得看板資訊
        print(f"5. 測試取得 {board_name} 看板資訊...")
        try:
            board_info = ptt_bot.get_board_info(board_name)
            if board_info:
                print(f"✅ 看板名稱: {getattr(board_info, 'board', 'N/A')}")
                print(f"✅ 看板標題: {getattr(board_info, 'title', 'N/A')}")
                print(f"✅ 線上人數: {getattr(board_info, 'online_user', 'N/A')}")
            else:
                print("❌ 無法取得看板資訊")
        except Exception as e:
            print(f"❌ 取得看板資訊失敗: {e}")
        
        # 6. 登出
        print("6. 登出 PTT...")
        ptt_bot.logout()
        print("✅ PTT 登出成功")
        
        return True
        
    except PyPtt.exceptions.LoginError as e:
        print(f"❌ 登入失敗: {e}")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_encoding():
    """測試編碼"""
    print("\n" + "=" * 60)
    print("編碼測試")
    print("=" * 60)
    
    # 測試中文字符
    test_strings = [
        "測試中文字符",
        "PTT 批踢踢實業坊",
        "看板文章列表",
        "使用者資訊",
        "推文內容"
    ]
    
    for test_str in test_strings:
        try:
            print(f"✅ {test_str}")
        except Exception as e:
            print(f"❌ 編碼錯誤: {e}")
    
    print("✅ 編碼測試完成")

if __name__ == "__main__":
    # 測試編碼
    test_encoding()
    
    # 測試 PTT
    success = test_ptt_simple()
    
    if success:
        print("\n🎉 所有測試通過！")
    else:
        print("\n❌ 測試失敗")
