#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試文章字典內容
"""

import sys
import json
import PyPtt
from config import Config

# 設定正確的編碼
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')

def test_post_dict_content():
    """測試文章字典內容"""
    print("=" * 60)
    print("PTT 文章字典內容測試")
    print("=" * 60)
    
    try:
        # 建立 PTT API 並登入
        ptt_bot = PyPtt.API()
        ptt_bot.login(
            ptt_id=Config.PTT_USERNAME,
            ptt_pw=Config.PTT_PASSWORD,
            kick_other_session=True
        )
        print("✅ PTT 登入成功")
        
        # 取得看板最新索引
        board_name = "Test"
        newest_index = ptt_bot.get_newest_index(
            index_type=PyPtt.NewIndex.BOARD,
            board=board_name
        )
        print(f"✅ 最新索引: {newest_index}")
        
        # 取得最近幾篇文章並檢查字典內容
        found_content = False
        for i in range(max(1, newest_index - 10), newest_index + 1):
            print(f"\n--- 檢查文章 {i} ---")
            try:
                post = ptt_bot.get_post(
                    board=board_name,
                    index=i
                )
                
                if post and isinstance(post, dict):
                    print(f"文章字典鍵值: {list(post.keys())}")
                    
                    # 顯示所有鍵值對
                    for key, value in post.items():
                        if value:  # 只顯示有值的項目
                            print(f"  {key}: {repr(value)} (類型: {type(value)})")
                            found_content = True
                    
                    # 如果找到有內容的文章，詳細分析
                    if any(post.values()):
                        print("\n詳細分析:")
                        
                        # 檢查標題
                        title = post.get('title', '')
                        if title:
                            print(f"標題: '{title}'")
                            print(f"標題編碼: {title.encode('utf-8') if isinstance(title, str) else 'N/A'}")
                        
                        # 檢查作者
                        author = post.get('author', '')
                        if author:
                            print(f"作者: '{author}'")
                        
                        # 檢查日期
                        date = post.get('date', '')
                        if date:
                            print(f"日期: '{date}'")
                        
                        break
                        
                elif post:
                    print(f"文章不是字典類型: {type(post)}")
                    print(f"文章內容: {post}")
                else:
                    print(f"文章 {i} 為空")
                    
            except Exception as e:
                print(f"取得文章 {i} 失敗: {e}")
        
        if not found_content:
            print("\n⚠️ 沒有找到任何有內容的文章")
            
            # 嘗試其他看板
            print("\n嘗試其他看板...")
            other_boards = ["Gossiping", "movie", "Beauty"]
            for board in other_boards:
                try:
                    print(f"\n檢查看板: {board}")
                    newest = ptt_bot.get_newest_index(
                        index_type=PyPtt.NewIndex.BOARD,
                        board=board
                    )
                    print(f"  最新索引: {newest}")
                    
                    # 只檢查最新一篇
                    post = ptt_bot.get_post(board=board, index=newest)
                    if post and isinstance(post, dict) and any(post.values()):
                        print(f"  找到有內容的文章!")
                        for key, value in post.items():
                            if value:
                                print(f"    {key}: {repr(value)[:100]}...")
                        break
                        
                except Exception as e:
                    print(f"  檢查看板 {board} 失敗: {e}")
        
        # 登出
        ptt_bot.logout()
        print("\n✅ PTT 登出成功")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_post_dict_content()
