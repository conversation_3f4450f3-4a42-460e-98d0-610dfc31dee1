#!/usr/bin/env python3
"""
PTT 推文功能測試腳本
專門測試 PTT 套件是否能正確抓取推文
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from ptt_client import PTTClient
from logger import ptt_logger

def test_ptt_comments():
    """測試 PTT 推文抓取功能"""
    print("=" * 60)
    print("PTT 推文功能測試")
    print("=" * 60)
    
    # 檢查配置
    config_errors = Config.validate_config()
    if config_errors:
        print("❌ 配置錯誤:")
        for error in config_errors:
            print(f"   - {error}")
        return False
    
    print(f"✅ 使用帳號: {Config.PTT_USERNAME}")
    
    # 建立 PTT 客戶端
    client = PTTClient()
    
    try:
        # 1. 測試登入
        print("\n1. 測試 PTT 登入...")
        if not client.login():
            print("❌ PTT 登入失敗")
            return False
        print("✅ PTT 登入成功")
        
        # 2. 測試取得看板文章列表
        print("\n2. 測試取得看板文章列表...")
        # 先嘗試 Gossiping 看板（通常有較多推文），如果失敗再用 Test 看板
        board_name = "Gossiping"
        posts = client.get_board_posts(board_name, max_posts=5)

        if not posts:
            print("⚠️ Gossiping 看板無法存取，改用 Test 看板")
            board_name = "Test"
            posts = client.get_board_posts(board_name, max_posts=5)
        
        if not posts:
            print("❌ 無法取得文章列表")
            return False
        
        print(f"✅ 成功取得 {len(posts)} 篇文章")
        
        # 3. 測試取得文章詳細內容和推文
        print("\n3. 測試取得文章詳細內容和推文...")
        
        comments_found = False
        for i, post in enumerate(posts):
            print(f"\n--- 測試文章 {i+1}: {post.get('title', 'N/A')} ---")
            print(f"AID: {post.get('aid', 'N/A')}")
            print(f"作者: {post.get('author', 'N/A')}")
            print(f"日期: {post.get('date', 'N/A')}")
            
            if not post.get('aid'):
                print("⚠️ 文章沒有 AID，跳過")
                continue
            
            # 取得文章詳細內容
            detailed_content = client.get_post_content(post['aid'], board_name)
            
            if detailed_content:
                print("✅ 成功取得文章詳細內容")
                
                # 檢查文章內容
                content = detailed_content.get('content', '')
                if content:
                    print(f"📄 文章內容長度: {len(content)} 字元")
                    # 顯示前100字元
                    preview = content[:100].replace('\n', ' ')
                    print(f"📄 內容預覽: {preview}...")
                else:
                    print("⚠️ 文章內容為空")
                
                # 檢查推文
                comments = detailed_content.get('comments', [])
                if comments:
                    print(f"💬 找到 {len(comments)} 則推文")
                    comments_found = True
                    
                    # 顯示前3則推文的詳細資訊
                    for j, comment in enumerate(comments[:3]):
                        print(f"  推文 {j+1}:")
                        print(f"    類型: {comment.get('type', 'N/A')}")
                        print(f"    作者: {comment.get('author', 'N/A')}")
                        print(f"    內容: {comment.get('content', 'N/A')}")
                        print(f"    IP: {comment.get('ip', 'N/A')}")
                        print(f"    時間: {comment.get('time', 'N/A')}")
                    
                    if len(comments) > 3:
                        print(f"  ... 還有 {len(comments) - 3} 則推文")
                    
                    # 統計推文類型
                    comment_types = {}
                    for comment in comments:
                        comment_type = comment.get('type', '未知')
                        comment_types[comment_type] = comment_types.get(comment_type, 0) + 1
                    
                    print(f"  推文類型統計: {comment_types}")
                    
                else:
                    print("📭 此文章沒有推文")
                
                # 檢查 IP 資訊
                ip = detailed_content.get('ip', '')
                if ip:
                    print(f"🌐 發文者 IP: {ip}")
                else:
                    print("🌐 無發文者 IP 資訊")
                
            else:
                print("❌ 無法取得文章詳細內容")
            
            # 如果已經找到有推文的文章，就不用繼續測試了
            if comments_found:
                break
            
            # 避免過於頻繁的請求
            time.sleep(1)
        
        # 4. 測試結果總結
        print("\n" + "=" * 60)
        print("測試結果總結")
        print("=" * 60)
        
        if comments_found:
            print("✅ PTT 推文抓取功能正常")
            print("✅ 能夠成功取得推文內容、作者、類型等資訊")
        else:
            print("⚠️ 未找到有推文的文章")
            print("⚠️ 可能是測試的文章都沒有推文，或推文抓取功能有問題")
            print("💡 建議：")
            print("   1. 嘗試測試其他看板（如 Gossiping）")
            print("   2. 檢查 PyPtt 版本是否支援推文抓取")
            print("   3. 確認 PTT 帳號權限是否足夠")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 登出
        try:
            if client.is_logged_in:
                client.logout()
                print("\n✅ 已登出 PTT")
        except:
            pass

def test_direct_pyptt_comments():
    """直接使用 PyPtt 測試推文功能"""
    print("\n" + "=" * 60)
    print("直接使用 PyPtt 測試推文功能")
    print("=" * 60)
    
    try:
        import PyPtt
        
        # 建立 PyPtt 物件
        ptt_bot = PyPtt.API()
        
        print("1. 登入 PTT...")
        ptt_bot.login(
            ptt_id=Config.PTT_USERNAME,
            ptt_pw=Config.PTT_PASSWORD,
            kick_other_session=True
        )
        print("✅ 登入成功")
        
        # 測試取得文章
        print("\n2. 測試取得文章...")
        # 先嘗試 Gossiping 看板，如果失敗再用 Test 看板
        board_name = "Gossiping"

        try:
            # 取得最新索引
            newest_index = ptt_bot.get_newest_index(
                index_type=PyPtt.NewIndex.BOARD,
                board=board_name
            )
            print(f"✅ {board_name} 看板最新索引: {newest_index}")
        except Exception as e:
            print(f"⚠️ 無法存取 {board_name} 看板: {e}")
            print("改用 Test 看板")
            board_name = "Test"

        # 取得最新索引（如果前面沒有取得的話）
        if 'newest_index' not in locals():
            newest_index = ptt_bot.get_newest_index(
                index_type=PyPtt.NewIndex.BOARD,
                board=board_name
            )
            print(f"✅ {board_name} 看板最新索引: {newest_index}")
        
        # 測試取得幾篇文章
        for i in range(max(1, newest_index - 2), newest_index + 1):
            print(f"\n--- 測試文章索引 {i} ---")
            try:
                post = ptt_bot.get_post(
                    board=board_name,
                    index=i
                )
                
                if post:
                    print(f"✅ 成功取得文章")
                    print(f"文章類型: {type(post)}")
                    
                    # 檢查文章屬性
                    if hasattr(post, 'title'):
                        print(f"標題: {post.title}")
                    if hasattr(post, 'author'):
                        print(f"作者: {post.author}")
                    if hasattr(post, 'date'):
                        print(f"日期: {post.date}")
                    if hasattr(post, 'aid'):
                        print(f"AID: {post.aid}")
                    
                    # 檢查推文相關屬性
                    comment_attrs = ['comments', 'pushes', 'responses', 'replies']
                    for attr in comment_attrs:
                        if hasattr(post, attr):
                            value = getattr(post, attr)
                            print(f"💬 {attr}: {value} (類型: {type(value)})")
                            
                            # 如果是列表，顯示詳細內容
                            if isinstance(value, list) and value:
                                print(f"   找到 {len(value)} 則推文:")
                                for j, comment in enumerate(value[:3]):
                                    print(f"     推文 {j+1}: {comment}")
                                if len(value) > 3:
                                    print(f"     ... 還有 {len(value) - 3} 則推文")
                    
                    # 檢查所有屬性
                    print(f"所有屬性: {[attr for attr in dir(post) if not attr.startswith('_')]}")
                    
                    break  # 找到一篇文章就停止
                else:
                    print(f"❌ 文章 {i} 不存在")
                    
            except Exception as e:
                print(f"❌ 取得文章 {i} 失敗: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接 PyPtt 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("開始 PTT 推文功能測試...")
    
    # 測試 1: 使用 PTTClient
    success1 = test_ptt_comments()
    
    # 測試 2: 直接使用 PyPtt
    success2 = test_direct_pyptt_comments()
    
    print("\n" + "=" * 60)
    print("最終測試結果")
    print("=" * 60)
    
    if success1:
        print("✅ PTTClient 推文測試通過")
    else:
        print("❌ PTTClient 推文測試失敗")
    
    if success2:
        print("✅ 直接 PyPtt 推文測試通過")
    else:
        print("❌ 直接 PyPtt 推文測試失敗")
    
    if success1 or success2:
        print("\n🎉 PTT 推文功能基本正常！")
    else:
        print("\n⚠️ PTT 推文功能可能有問題，需要進一步檢查")
