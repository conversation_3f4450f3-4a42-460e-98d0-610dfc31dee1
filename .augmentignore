# ===========================================
# PTT 自動登入系統 - Augment AI 忽略檔案
# ===========================================
# 此檔案指定哪些檔案和目錄不應被 Augment AI 讀取
# 用於保護敏感資料、個人資訊和機密配置

# ===========================================
# 環境變數和配置檔案
# ===========================================
.env
.env.local
.env.production
.env.development
.env.staging
.env.test
# 保留範本檔案可見
!.env.example
config.ini
config.yaml
config.yml
secrets.json
credentials.json
auth.json

# ===========================================
# PTT 相關敏感資料
# ===========================================
# PTT 帳號密碼檔案
ptt_credentials.*
ptt_config.*
login_info.*

# PTT 爬文結果（可能包含個人資訊）
crawl_results/
crawl_data/
ptt_data/
*.crawl
*_crawl_*.json
today_crawl_*.json

# PTT 使用者資料
user_data/
users.json
user_list.*
user_profiles.*

# ===========================================
# 資料庫相關敏感資料
# ===========================================
# Azure Cosmos DB 連接字串和金鑰
cosmos_config.*
azure_credentials.*
connection_strings.*
database_config.*

# 資料庫備份檔案
*.sql
*.db
*.sqlite
*.sqlite3
backup/
dumps/

# 圖形資料庫查詢結果（可能包含敏感關係）
graph_results/
graph_data/
network_data/
relationship_data/

# ===========================================
# 日誌檔案（可能包含敏感資訊）
# ===========================================
logs/
*.log
log_*.txt
debug_*.txt
error_*.txt
access_*.log
audit_*.log

# 特定日誌檔案
ptt_system_*.log
crawler_*.log
api_*.log

# ===========================================
# 測試和開發檔案
# ===========================================
# 測試資料（可能包含真實資料）
test_data/
mock_data/
sample_data/
fixtures/

# 測試結果和報告
test_results/
test_reports/
coverage_reports/
*_test_result.*
test_report_*.txt

# 開發時的臨時檔案
temp/
tmp/
scratch/
playground/
experiments/

# ===========================================
# 部署和運維相關
# ===========================================
# Azure 部署檔案
.azure/
azure_config.*
deployment_config.*
publish_profile.*
*.pubxml

# Docker 相關敏感檔案
docker-compose.override.yml
.dockerignore
Dockerfile.prod

# Kubernetes 配置
k8s/
kubernetes/
*.k8s.yaml
*-secret.yaml

# ===========================================
# 個人和機密檔案
# ===========================================
# 個人筆記和文檔
notes/
personal/
private/
confidential/
internal/

# 個人配置檔案
.vscode/settings.json
.idea/
*.user
*.suo

# 備份檔案
*.bak
*.backup
*.old
*_backup.*

# ===========================================
# 第三方服務金鑰
# ===========================================
# API 金鑰檔案
api_keys.*
service_keys.*
access_tokens.*
refresh_tokens.*

# 第三方服務配置
firebase_config.*
aws_config.*
gcp_config.*
oauth_config.*

# ===========================================
# 加密和安全相關
# ===========================================
# 私鑰和憑證
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer
private_keys/
certificates/

# 加密檔案
*.enc
*.encrypted
*.gpg
*.asc

# ===========================================
# 快取和臨時檔案
# ===========================================
# 應用程式快取
cache/
.cache/
__pycache__/
*.pyc
*.pyo

# 前端快取
node_modules/
.npm/
.yarn/
dist/
build/

# ===========================================
# 系統和 IDE 檔案
# ===========================================
# 作業系統檔案
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# IDE 和編輯器檔案
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath

# ===========================================
# 特定敏感檔案模式
# ===========================================
# 包含 "secret", "password", "key" 的檔案
*secret*
*password*
*passwd*
*credential*
*auth*
*token*
*key*

# 包含 "private", "confidential" 的檔案
*private*
*confidential*
*internal*
*sensitive*

# 包含個人資訊的檔案
*personal*
*user_info*
*profile*
*contact*

# ===========================================
# 輸出和結果檔案
# ===========================================
# 分析結果（可能包含敏感洞察）
analysis_results/
reports/
statistics/
metrics/

# 匯出檔案
exports/
downloads/
output/
results/

# 圖表和視覺化（可能洩露資料模式）
charts/
graphs/
visualizations/
*.png
*.jpg
*.jpeg
*.gif
*.svg
*.pdf

# ===========================================
# 特殊保護檔案
# ===========================================
# 此設定檔案本身的備份
.augmentignore.bak
.augmentignore.old

# 安全設定指南（包含敏感設定說明）
SECURITY_SETUP.md
security_guide.*
setup_instructions.*

# 環境設定工具（可能包含預設敏感值）
setup_env.*
configure_env.*
env_setup.*
