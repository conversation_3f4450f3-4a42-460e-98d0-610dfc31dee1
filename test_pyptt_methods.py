#!/usr/bin/env python3
"""
測試 PyPtt API 的可用方法，特別是爬文相關功能
"""

import PyPtt
import inspect
from config import Config

def test_pyptt_methods():
    """測試 PyPtt 的可用方法"""
    print("=== 測試 PyPtt 可用方法 ===")
    
    try:
        # 建立 PTT 物件
        print("1. 建立 PTT 物件...")
        ptt_bot = PyPtt.API()
        print("✓ PTT 物件建立成功")
        
        # 取得所有方法
        print("\n2. 分析可用方法...")
        all_methods = [method for method in dir(ptt_bot) if not method.startswith('_')]
        print(f"✓ 總共有 {len(all_methods)} 個公開方法")
        
        # 分類方法
        login_methods = [m for m in all_methods if 'login' in m.lower() or 'logout' in m.lower()]
        board_methods = [m for m in all_methods if 'board' in m.lower()]
        post_methods = [m for m in all_methods if 'post' in m.lower()]
        get_methods = [m for m in all_methods if m.startswith('get_')]
        search_methods = [m for m in all_methods if 'search' in m.lower()]
        
        print("\n3. 方法分類:")
        print(f"   登入相關: {login_methods}")
        print(f"   看板相關: {board_methods}")
        print(f"   文章相關: {post_methods}")
        print(f"   取得資料: {get_methods}")
        print(f"   搜尋相關: {search_methods}")
        
        # 檢查重要方法的簽名
        print("\n4. 重要方法簽名:")
        important_methods = ['login', 'goto_board', 'get_newest_index', 'search_post']
        
        for method_name in important_methods:
            if hasattr(ptt_bot, method_name):
                method = getattr(ptt_bot, method_name)
                try:
                    signature = inspect.signature(method)
                    print(f"   {method_name}{signature}")
                except Exception as e:
                    print(f"   {method_name}: 無法取得簽名 ({e})")
            else:
                print(f"   {method_name}: 方法不存在")
        
        # 檢查是否有取得文章內容的方法
        print("\n5. 尋找文章內容相關方法:")
        content_methods = [m for m in all_methods if any(keyword in m.lower() for keyword in ['content', 'article', 'comment', 'push'])]
        if content_methods:
            print(f"   找到相關方法: {content_methods}")
            for method_name in content_methods:
                method = getattr(ptt_bot, method_name)
                try:
                    signature = inspect.signature(method)
                    print(f"     {method_name}{signature}")
                except:
                    print(f"     {method_name}: 無法取得簽名")
        else:
            print("   未找到明顯的文章內容方法")
        
        # 檢查 PyPtt 的常數和枚舉
        print("\n6. 檢查 PyPtt 常數和枚舉:")
        for attr_name in dir(PyPtt):
            if not attr_name.startswith('_') and attr_name[0].isupper():
                attr = getattr(PyPtt, attr_name)
                print(f"   {attr_name}: {type(attr)} - {attr}")
        
        return True
        
    except Exception as e:
        print(f"✗ 測試失敗: {e}")
        return False

def test_login_and_explore():
    """登入後測試更多功能"""
    print("\n=== 登入後功能測試 ===")
    
    # 檢查是否有設定帳密
    if not Config.PTT_USERNAME or not Config.PTT_PASSWORD:
        print("⚠️ 未設定 PTT 帳密，跳過登入測試")
        return False
    
    try:
        print("1. 建立並登入 PTT...")
        ptt_bot = PyPtt.API()
        
        ptt_bot.login(
            ptt_id=Config.PTT_USERNAME,
            ptt_pw=Config.PTT_PASSWORD,
            kick_other_session=True
        )
        print("✓ 登入成功")
        
        # 測試進入看板
        print("\n2. 測試進入看板...")
        ptt_bot.goto_board('Test')
        print("✓ 成功進入 Test 看板")
        
        # 測試取得最新文章
        print("\n3. 測試取得最新文章...")
        try:
            newest_list = ptt_bot.get_newest_index(
                index=PyPtt.NewIndex.BOARD,
                board='Test'
            )
            print(f"✓ 取得 {len(newest_list)} 篇最新文章")
            
            if newest_list:
                print("   第一篇文章資訊:")
                first_post = newest_list[0]
                print(f"     類型: {type(first_post)}")
                print(f"     屬性: {[attr for attr in dir(first_post) if not attr.startswith('_')]}")
                
                # 嘗試取得文章詳細內容
                print("\n4. 嘗試取得文章詳細內容...")
                if hasattr(first_post, 'aid') or hasattr(first_post, 'id'):
                    post_id = getattr(first_post, 'aid', getattr(first_post, 'id', None))
                    print(f"   文章ID: {post_id}")
                    
                    # 檢查是否有 get_post 方法
                    if hasattr(ptt_bot, 'get_post'):
                        try:
                            post_content = ptt_bot.get_post(post_id, board='Test')
                            print(f"✓ 成功取得文章內容: {type(post_content)}")
                            if hasattr(post_content, 'content'):
                                print(f"   內容長度: {len(post_content.content)}")
                            if hasattr(post_content, 'comments'):
                                print(f"   推文數量: {len(post_content.comments)}")
                        except Exception as e:
                            print(f"✗ get_post 失敗: {e}")
                    else:
                        print("✗ 沒有 get_post 方法")
                
        except Exception as e:
            print(f"✗ 取得最新文章失敗: {e}")
        
        # 登出
        print("\n5. 登出...")
        ptt_bot.logout()
        print("✓ 登出成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 登入測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("🚀 PyPtt 方法探索開始...")
    print("="*50)
    
    # 基本方法測試
    success1 = test_pyptt_methods()
    
    if success1:
        # 登入後功能測試
        success2 = test_login_and_explore()
    
    print("\n🎉 測試完成！")
