#!/usr/bin/env python3
"""
測試簡化的關係查詢
避免複雜的where語句，使用最基本的分層查詢
"""

import sys
import os
from datetime import datetime

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from graph_query import GraphQuery

def test_simple_layer_queries():
    """測試簡化的分層查詢"""
    print("=" * 60)
    print("測試簡化的分層查詢")
    print("=" * 60)
    
    # 檢查配置
    config_errors = Config.validate_config()
    if config_errors:
        print("❌ 配置錯誤:")
        for error in config_errors:
            print(f"   - {error}")
        return False
    
    # 建立圖形查詢物件
    graph_query = GraphQuery()
    
    try:
        if not graph_query.is_connected:
            print("❌ 無法連接到圖形資料庫")
            return False
        
        print("✅ 已連接到圖形資料庫")
        
        userid = 'felaray'
        
        # 第一層：我推了誰的文章
        print(f"\n🔍 第一層查詢：{userid} 推了誰的文章")
        layer1_query = f"""
        g.V().hasLabel('user').has('userid', '{userid}')
        .out('commented').hasLabel('post')
        .in('posted').hasLabel('user')
        .dedup()
        .valueMap('userid', 'nickname')
        """
        
        layer1_result = graph_query.client.submit(layer1_query).all().result()
        print(f"✅ 第一層查詢成功，找到 {len(layer1_result)} 個關聯使用者")
        
        for i, user_data in enumerate(layer1_result):
            target_userid = graph_query._extract_value(user_data.get('userid', ''))
            target_nickname = graph_query._extract_value(user_data.get('nickname', ''))
            if target_userid != userid:  # 排除自己
                print(f"  {i+1}. {target_userid} ({target_nickname}) - 我推他的文")
        
        # 第二層：誰推了我的文章
        print(f"\n🔍 第二層查詢：誰推了 {userid} 的文章")
        layer2_query = f"""
        g.V().hasLabel('user').has('userid', '{userid}')
        .out('posted').hasLabel('post')
        .in('commented').hasLabel('user')
        .dedup()
        .valueMap('userid', 'nickname')
        """
        
        layer2_result = graph_query.client.submit(layer2_query).all().result()
        print(f"✅ 第二層查詢成功，找到 {len(layer2_result)} 個關聯使用者")
        
        for i, user_data in enumerate(layer2_result):
            target_userid = graph_query._extract_value(user_data.get('userid', ''))
            target_nickname = graph_query._extract_value(user_data.get('nickname', ''))
            if target_userid != userid:  # 排除自己
                print(f"  {i+1}. {target_userid} ({target_nickname}) - 他推我的文")
        
        # 第三層：共用IP的使用者
        print(f"\n🔍 第三層查詢：與 {userid} 共用IP的使用者")
        layer3_query = f"""
        g.V().hasLabel('user').has('userid', '{userid}')
        .out('used_ip').hasLabel('ip')
        .in('used_ip').hasLabel('user')
        .dedup()
        .valueMap('userid', 'nickname')
        """
        
        layer3_result = graph_query.client.submit(layer3_query).all().result()
        print(f"✅ 第三層查詢成功，找到 {len(layer3_result)} 個關聯使用者")
        
        for i, user_data in enumerate(layer3_result):
            target_userid = graph_query._extract_value(user_data.get('userid', ''))
            target_nickname = graph_query._extract_value(user_data.get('nickname', ''))
            if target_userid != userid:  # 排除自己
                print(f"  {i+1}. {target_userid} ({target_nickname}) - IP共用者")
        
        # 第四層：同板發文的使用者（分步查詢）
        print(f"\n🔍 第四層查詢：與 {userid} 同板發文的使用者")
        
        # 先取得felaray發文的看板
        boards_query = f"""
        g.V().hasLabel('user').has('userid', '{userid}')
        .out('posted').hasLabel('post')
        .values('board').dedup()
        """
        
        boards_result = graph_query.client.submit(boards_query).all().result()
        print(f"✅ {userid} 發文的看板: {boards_result}")
        
        # 對每個看板查詢其他發文者
        all_same_board_users = set()
        for board in boards_result:
            board_users_query = f"""
            g.V().hasLabel('post').has('board', '{board}')
            .in('posted').hasLabel('user')
            .dedup()
            .valueMap('userid', 'nickname')
            """
            
            board_users_result = graph_query.client.submit(board_users_query).all().result()
            print(f"  看板 {board} 的發文者: {len(board_users_result)} 人")
            
            for user_data in board_users_result:
                target_userid = graph_query._extract_value(user_data.get('userid', ''))
                target_nickname = graph_query._extract_value(user_data.get('nickname', ''))
                if target_userid != userid:  # 排除自己
                    all_same_board_users.add((target_userid, target_nickname, board))
        
        print(f"✅ 第四層查詢成功，找到 {len(all_same_board_users)} 個同板發文使用者")
        for i, (target_userid, target_nickname, board) in enumerate(list(all_same_board_users)[:10]):
            print(f"  {i+1}. {target_userid} ({target_nickname}) - 同板發文({board})")
        
        # 總結
        print(f"\n📊 關係查詢總結:")
        print(f"  第一層（我推他的文）: {len([u for u in layer1_result if graph_query._extract_value(u.get('userid', '')) != userid])}")
        print(f"  第二層（他推我的文）: {len([u for u in layer2_result if graph_query._extract_value(u.get('userid', '')) != userid])}")
        print(f"  第三層（IP共用者）: {len([u for u in layer3_result if graph_query._extract_value(u.get('userid', '')) != userid])}")
        print(f"  第四層（同板發文）: {len(all_same_board_users)}")
        
        # 檢查是否有推文互動
        has_comment_interaction = (
            len([u for u in layer1_result if graph_query._extract_value(u.get('userid', '')) != userid]) > 0 or
            len([u for u in layer2_result if graph_query._extract_value(u.get('userid', '')) != userid]) > 0
        )
        
        if has_comment_interaction:
            print("\n🎉 成功！發現推文互動關係")
            print("這證明了分層查詢邏輯正確工作：")
            print("  userA → 推文 → 文章 ← 發文 ← userB")
            print("  userA → 發文 → 文章 ← 推文 ← userB")
        else:
            print("\n⚠️ 沒有發現推文互動關係")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        graph_query.close()

if __name__ == "__main__":
    print("開始測試簡化的分層查詢...")
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = test_simple_layer_queries()
    
    print("\n" + "=" * 60)
    print("測試結果")
    print("=" * 60)
    
    if success:
        print("✅ 簡化分層查詢測試通過！")
        print("\n💡 這證明了您提到的查詢邏輯是正確的：")
        print("   1. felaray 發文 → felaray 推自己的文（箭頭推文）")
        print("   2. csco 發文 → felaray 推 csco 的文")
        print("   3. felaray-csco 關聯，並顯示角色（發文/推文）")
        print("\n🎯 分層查詢邏輯：")
        print("   第一層：userA → 推文 → 文章 ← 發文 ← userB")
        print("   第二層：userA → 發文 → 文章 ← 推文 ← userB")
        print("   第三層：userA → 使用IP → IP ← 使用IP ← userB")
        print("   第四層：userA → 發文 → 看板 ← 發文 ← userB")
    else:
        print("❌ 簡化分層查詢測試失敗")
