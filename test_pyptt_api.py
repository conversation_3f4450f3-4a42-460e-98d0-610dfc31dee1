#!/usr/bin/env python3
"""
測試 PyPtt API 的基本功能
不需要真實帳號，只測試 API 的可用性
"""

import PyPtt
import sys

def test_pyptt_basic():
    """測試 PyPtt 基本功能"""
    print("=== 測試 PyPtt 基本功能 ===")

    try:
        # 測試不同的建立方式
        print("1. 測試建立 PTT 物件...")

        # 嘗試不同的建立方式
        creation_methods = [
            ('PyPtt.PTT()', lambda: PyPtt.PTT()),
            ('PyPtt.API()', lambda: PyPtt.API()),
            ('PyPtt.PTT', lambda: PyPtt.PTT),
            ('PyPtt.API', lambda: PyPtt.API),
        ]

        ptt_bot = None
        for method_name, method_func in creation_methods:
            try:
                print(f"   嘗試: {method_name}")
                ptt_bot = method_func()
                print(f"   ✓ {method_name} 建立成功")
                break
            except Exception as e:
                print(f"   ✗ {method_name} 失敗: {e}")

        if ptt_bot is None:
            print("   ✗ 無法建立 PTT 物件")
            return False

        # 測試 2: 檢查 login 方法的參數
        print("2. 檢查 login 方法...")
        import inspect
        login_signature = inspect.signature(ptt_bot.login)
        print(f"   ✓ login 方法參數: {login_signature}")

        # 測試 3: 檢查可用的方法
        print("3. 檢查可用的方法...")
        methods = [method for method in dir(ptt_bot) if not method.startswith('_')]
        print(f"   ✓ 可用方法數量: {len(methods)}")
        print("   主要方法:")
        important_methods = ['login', 'logout', 'get_user', 'get_call_status', 'goto_board', 'post']
        for method in important_methods:
            if hasattr(ptt_bot, method):
                print(f"     ✓ {method}")
            else:
                print(f"     ✗ {method} (不存在)")

        # 測試 4: 尋找爬文相關方法
        print("4. 尋找爬文相關方法...")
        crawl_methods = [m for m in methods if any(keyword in m.lower() for keyword in ['get', 'search', 'post', 'content', 'comment', 'push'])]
        print(f"   爬文相關方法: {crawl_methods}")

        # 測試 5: 檢查重要方法的簽名
        print("5. 重要方法簽名:")
        for method_name in ['get_newest_index', 'search_post', 'get_post'] + crawl_methods[:5]:
            if hasattr(ptt_bot, method_name):
                try:
                    method = getattr(ptt_bot, method_name)
                    signature = inspect.signature(method)
                    print(f"   {method_name}{signature}")
                except Exception as e:
                    print(f"   {method_name}: 無法取得簽名 ({e})")

        # 測試 6: 檢查 PyPtt 的常數和枚舉
        print("6. 檢查 PyPtt 常數和枚舉:")
        for attr_name in dir(PyPtt):
            if not attr_name.startswith('_') and attr_name[0].isupper():
                attr = getattr(PyPtt, attr_name)
                print(f"   {attr_name}: {type(attr)}")
                if hasattr(attr, '__members__'):  # 枚舉類型
                    print(f"     成員: {list(attr.__members__.keys())}")

        return True
        
    except Exception as e:
        print(f"   ✗ 錯誤: {e}")
        return False

def test_login_with_fake_credentials():
    """使用假帳號測試登入方法的參數"""
    print("\n=== 測試登入方法參數 ===")

    try:
        # 嘗試建立 PTT 物件
        ptt_bot = None
        try:
            ptt_bot = PyPtt.API()
        except:
            try:
                ptt_bot = PyPtt.PTT()
            except:
                print("   ✗ 無法建立 PTT 物件")
                return False
        
        # 嘗試不同的參數組合來找出正確的 API
        test_cases = [
            {
                'name': 'ptt_id + ptt_pw + kick_other_session',
                'params': {'ptt_id': 'test', 'ptt_pw': 'test', 'kick_other_session': True}
            },
            {
                'name': 'username + password + kick_other_session',
                'params': {'username': 'test', 'password': 'test', 'kick_other_session': True}
            },
            {
                'name': 'ptt_id + password + kick_other_login',
                'params': {'ptt_id': 'test', 'password': 'test', 'kick_other_login': True}
            },
            {
                'name': '位置參數 (id, pw, kick)',
                'params': None,
                'args': ('test', 'test', True)
            }
        ]
        
        for test_case in test_cases:
            try:
                print(f"測試: {test_case['name']}")
                
                if test_case['params']:
                    ptt_bot.login(**test_case['params'])
                else:
                    ptt_bot.login(*test_case['args'])
                    
                print(f"   ✓ 參數接受 (會因為假帳號而登入失敗，但參數正確)")
                break  # 找到正確的參數格式就停止
                
            except TypeError as e:
                if "unexpected keyword argument" in str(e) or "takes" in str(e):
                    print(f"   ✗ 參數錯誤: {e}")
                else:
                    print(f"   ✓ 參數正確，登入失敗: {e}")
                    break
            except Exception as e:
                print(f"   ✓ 參數正確，登入失敗: {e}")
                break
        
        return True
        
    except Exception as e:
        print(f"   ✗ 測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("PyPtt API 測試工具")
    print("=" * 50)
    
    # 檢查 PyPtt 版本
    try:
        version = PyPtt.__version__
        print(f"PyPtt 版本: {version}")
    except:
        print("無法取得 PyPtt 版本")
    
    print()
    
    # 執行測試
    tests = [
        ("基本功能測試", test_pyptt_basic),
        ("登入參數測試", test_login_with_fake_credentials),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通過")
            else:
                print(f"✗ {test_name} 失敗")
        except Exception as e:
            print(f"✗ {test_name} 異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"測試結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試都通過了！")
        return 0
    else:
        print("⚠ 部分測試失敗，請檢查 PyPtt 安裝和版本。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
