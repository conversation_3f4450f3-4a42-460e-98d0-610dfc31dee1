#!/usr/bin/env python3
"""
測試 API 查詢功能
"""

from config import Config
from gremlin_python.driver import client, serializer
from gremlin_python.driver.aiohttp.transport import AiohttpTransport

def test_direct_queries():
    """直接測試 Gremlin 查詢"""
    print("=== 直接測試 Gremlin 查詢 ===")
    
    try:
        # 建立客戶端
        transport_factory = lambda: AiohttpTransport()
        
        gremlin_client = client.Client(
            Config.COSMOS_DB_ENDPOINT,
            'g',
            username=f"/dbs/{Config.COSMOS_DB_DATABASE}/colls/{Config.COSMOS_DB_COLLECTION}",
            password=Config.COSMOS_DB_KEY,
            message_serializer=serializer.GraphSONSerializersV2d0(),
            transport_factory=transport_factory
        )
        
        print("✓ 客戶端建立成功")
        
        # 1. 測試基本查詢
        print("\n1. 測試頂點總數...")
        result = gremlin_client.submit("g.V().count()").all().result()
        print(f"總頂點數: {result[0] if result else 0}")
        
        # 2. 測試使用者查詢
        print("\n2. 測試使用者查詢...")
        user_query = "g.V().hasLabel('user')"
        result = gremlin_client.submit(user_query).all().result()
        print(f"使用者頂點數: {len(result)}")
        for i, user in enumerate(result):
            print(f"  使用者 {i+1}: {user}")
        
        # 3. 測試使用者屬性查詢
        print("\n3. 測試使用者屬性查詢...")
        user_props_query = "g.V().hasLabel('user').valueMap()"
        result = gremlin_client.submit(user_props_query).all().result()
        print(f"使用者屬性: {len(result)} 個")
        for i, props in enumerate(result):
            print(f"  使用者屬性 {i+1}: {props}")
        
        # 4. 測試 IP 查詢
        print("\n4. 測試 IP 查詢...")
        ip_query = "g.V().hasLabel('ip')"
        result = gremlin_client.submit(ip_query).all().result()
        print(f"IP 頂點數: {len(result)}")
        for i, ip in enumerate(result):
            print(f"  IP {i+1}: {ip}")
        
        # 5. 測試 IP 屬性查詢
        print("\n5. 測試 IP 屬性查詢...")
        ip_props_query = "g.V().hasLabel('ip').valueMap()"
        result = gremlin_client.submit(ip_props_query).all().result()
        print(f"IP 屬性: {len(result)} 個")
        for i, props in enumerate(result):
            print(f"  IP 屬性 {i+1}: {props}")
        
        # 6. 測試文章查詢
        print("\n6. 測試文章查詢...")
        post_query = "g.V().hasLabel('post')"
        result = gremlin_client.submit(post_query).all().result()
        print(f"文章頂點數: {len(result)}")
        for i, post in enumerate(result):
            print(f"  文章 {i+1}: {post}")
        
        # 7. 測試邊查詢
        print("\n7. 測試邊查詢...")
        edge_query = "g.E().count()"
        result = gremlin_client.submit(edge_query).all().result()
        print(f"總邊數: {result[0] if result else 0}")
        
        # 8. 測試特定使用者查詢
        print("\n8. 測試特定使用者查詢...")
        specific_user_query = "g.V().hasLabel('user').has('username', 'test_user1').valueMap()"
        result = gremlin_client.submit(specific_user_query).all().result()
        print(f"test_user1 資料: {result}")
        
        gremlin_client.close()
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoints():
    """測試 API 端點"""
    print("\n=== 測試 API 端點 ===")
    
    import requests
    
    base_url = "http://127.0.0.1:5000/api"
    
    try:
        # 1. 測試健康檢查
        print("1. 測試健康檢查...")
        response = requests.get(f"{base_url}/system/health")
        print(f"健康檢查: {response.status_code} - {response.json()}")
        
        # 2. 測試圖形統計
        print("\n2. 測試圖形統計...")
        response = requests.get(f"{base_url}/graph-stats")
        print(f"圖形統計: {response.status_code} - {response.json()}")
        
        # 3. 測試使用者列表
        print("\n3. 測試使用者列表...")
        response = requests.get(f"{base_url}/users")
        print(f"使用者列表: {response.status_code} - {response.json()}")
        
        # 4. 測試 IP 列表
        print("\n4. 測試 IP 列表...")
        response = requests.get(f"{base_url}/ips")
        print(f"IP 列表: {response.status_code} - {response.json()}")
        
        # 5. 測試使用者 IP 分析
        print("\n5. 測試使用者 IP 分析...")
        response = requests.get(f"{base_url}/user-ip-analysis?username=test_user1")
        print(f"使用者 IP 分析: {response.status_code} - {response.json()}")
        
        return True
        
    except Exception as e:
        print(f"❌ API 測試失敗: {e}")
        return False

if __name__ == "__main__":
    print("API 查詢功能測試")
    print("=" * 50)
    
    # 先測試直接查詢
    if test_direct_queries():
        print("\n✓ 直接查詢測試通過")
        
        # 再測試 API
        if test_api_endpoints():
            print("\n✓ API 測試通過")
        else:
            print("\n✗ API 測試失敗")
    else:
        print("\n✗ 直接查詢測試失敗")
    
    print("\n測試完成")
