#!/usr/bin/env python3
"""
測試 PTT 客戶端的爬文功能
"""

import json
from ptt_client import PTTClient
from config import Config
from logger import ptt_logger

def test_get_board_posts():
    """測試取得看板文章列表功能"""
    print("=== 測試取得看板文章列表 ===")
    
    # 檢查配置
    if not Config.PTT_USERNAME or not Config.PTT_PASSWORD:
        print("⚠️ 未設定 PTT 帳密，跳過測試")
        return False
    
    client = PTTClient()
    
    try:
        # 登入
        print("1. 登入 PTT...")
        if not client.login():
            print("✗ 登入失敗")
            return False
        print("✓ 登入成功")
        
        # 測試取得 Test 看板文章
        print("2. 取得 Test 看板文章列表...")
        posts = client.get_board_posts('Test', max_posts=5)
        
        if posts:
            print(f"✓ 成功取得 {len(posts)} 篇文章")
            print("   文章列表:")
            for i, post in enumerate(posts, 1):
                print(f"     {i}. {post['title']}")
                print(f"        作者: {post['author']} | 日期: {post['date']}")
                print(f"        AID: {post['aid']} | IP: {post.get('ip', '無')}")
        else:
            print("✗ 無法取得文章列表")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 測試失敗: {e}")
        return False
    
    finally:
        client.disconnect()

def test_get_post_content():
    """測試取得文章內容功能"""
    print("\n=== 測試取得文章內容 ===")
    
    # 檢查配置
    if not Config.PTT_USERNAME or not Config.PTT_PASSWORD:
        print("⚠️ 未設定 PTT 帳密，跳過測試")
        return False
    
    client = PTTClient()
    
    try:
        # 登入
        print("1. 登入 PTT...")
        if not client.login():
            print("✗ 登入失敗")
            return False
        print("✓ 登入成功")
        
        # 先取得文章列表
        print("2. 取得文章列表...")
        posts = client.get_board_posts('Test', max_posts=3)
        
        if not posts:
            print("✗ 無法取得文章列表")
            return False
        
        # 測試取得第一篇文章的詳細內容
        first_post = posts[0]
        if not first_post['aid']:
            print("✗ 第一篇文章沒有 AID")
            return False
        
        print(f"3. 取得文章詳細內容: {first_post['title']}")
        content = client.get_post_content(first_post['aid'], 'Test')
        
        if content:
            print("✓ 成功取得文章內容")
            print(f"   標題: {content['title']}")
            print(f"   作者: {content['author']}")
            print(f"   內容長度: {len(content['content'])} 字元")
            print(f"   推文數量: {len(content['comments'])}")
            print(f"   發文者IP: {content.get('ip', '無')}")
            
            if content['comments']:
                print("   前3則推文:")
                for i, comment in enumerate(content['comments'][:3], 1):
                    print(f"     {i}. [{comment['type']}] {comment['author']}: {comment['content'][:50]}...")
        else:
            print("✗ 無法取得文章內容")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 測試失敗: {e}")
        return False
    
    finally:
        client.disconnect()

def test_crawl_board_data():
    """測試完整爬取看板資料功能"""
    print("\n=== 測試完整爬取看板資料 ===")
    
    # 檢查配置
    if not Config.PTT_USERNAME or not Config.PTT_PASSWORD:
        print("⚠️ 未設定 PTT 帳密，跳過測試")
        return False
    
    client = PTTClient()
    
    try:
        # 登入
        print("1. 登入 PTT...")
        if not client.login():
            print("✗ 登入失敗")
            return False
        print("✓ 登入成功")
        
        # 測試爬取 Test 看板資料（只取2篇文章以節省時間）
        print("2. 爬取 Test 看板資料...")
        board_data = client.crawl_board_data('Test', max_posts=2, include_content=True)
        
        if board_data:
            print("✓ 成功爬取看板資料")
            print(f"   看板: {board_data['board']}")
            print(f"   爬取時間: {board_data['crawl_time']}")
            print(f"   文章數量: {len(board_data['posts'])}")
            
            # 顯示詳細資訊
            for i, post in enumerate(board_data['posts'], 1):
                print(f"   文章 {i}:")
                print(f"     標題: {post['title']}")
                print(f"     作者: {post['author']}")
                print(f"     內容長度: {len(post.get('content', ''))} 字元")
                print(f"     推文數量: {len(post.get('comments', []))}")
                print(f"     IP: {post.get('ip', '無')}")
            
            # 儲存為 JSON 檔案
            output_file = f"test_crawl_data_{board_data['board']}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(board_data, f, ensure_ascii=False, indent=2)
            print(f"   ✓ 資料已儲存至 {output_file}")
            
        else:
            print("✗ 無法爬取看板資料")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 測試失敗: {e}")
        return False
    
    finally:
        client.disconnect()

def main():
    """主測試函數"""
    print("PTT 爬文功能測試")
    print("=" * 50)
    
    # 檢查配置
    config_errors = Config.validate_config()
    if config_errors:
        print("配置錯誤:")
        for error in config_errors:
            print(f"  - {error}")
        print("\n請先設定 PTT_USERNAME 和 PTT_PASSWORD 環境變數")
        return 1
    
    print(f"使用帳號: {Config.PTT_USERNAME}")
    print()
    
    # 執行測試
    tests = [
        ("取得看板文章列表", test_get_board_posts),
        ("取得文章內容", test_get_post_content),
        ("完整爬取看板資料", test_crawl_board_data),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 測試通過")
            else:
                print(f"✗ {test_name} 測試失敗")
        except Exception as e:
            print(f"✗ {test_name} 測試異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"測試結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有爬文功能測試都通過了！")
        return 0
    else:
        print("⚠ 部分測試失敗，請檢查 PTT 連線和帳號設定。")
        return 1

if __name__ == "__main__":
    exit(main())
