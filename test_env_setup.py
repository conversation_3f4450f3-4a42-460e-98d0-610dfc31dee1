"""
測試環境變數設定
"""

import os
import sys
from pathlib import Path

# 添加專案根目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config


def test_env_setup():
    """測試環境變數設定"""
    print("🔍 檢查環境變數設定")
    print("=" * 50)
    
    # 檢查 .env 檔案是否存在
    env_file = Path('.env')
    if env_file.exists():
        print("✅ .env 檔案存在")
        
        # 讀取檔案內容（不顯示敏感資訊）
        with open(env_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📄 .env 檔案包含 {len(lines)} 行")
        
        # 檢查必要的環境變數
        required_vars = [
            'PTT_USERNAME',
            'PTT_PASSWORD', 
            'COSMOS_DB_ENDPOINT',
            'COSMOS_DB_KEY',
            'COSMOS_DB_DATABASE',
            'COSMOS_DB_COLLECTION'
        ]
        
        print("\n🔑 環境變數檢查:")
        for var in required_vars:
            value = os.getenv(var)
            if value:
                if 'PASSWORD' in var or 'KEY' in var:
                    # 隱藏敏感資訊
                    display_value = value[:3] + '*' * (len(value) - 6) + value[-3:] if len(value) > 6 else '***'
                else:
                    display_value = value
                print(f"  ✅ {var}: {display_value}")
            else:
                print(f"  ❌ {var}: 未設定")
    else:
        print("❌ .env 檔案不存在")
        print("請執行 setup_env.py 或 setup_env.bat 來建立環境變數設定")
    
    print("\n🔧 配置驗證:")
    
    # 測試 PTT 配置
    ptt_result = Config.validate_config()
    print(f"PTT 配置: {'✅ 有效' if ptt_result['is_valid'] else '❌ 無效'}")
    if not ptt_result['is_valid']:
        for error in ptt_result['errors']:
            print(f"  - {error['message']}")
    
    # 測試 Cosmos DB 配置
    cosmos_result = Config.validate_cosmos_config()
    print(f"Cosmos DB 配置: {'✅ 有效' if cosmos_result['is_valid'] else '❌ 無效'}")
    if not cosmos_result['is_valid']:
        for error in cosmos_result['errors']:
            print(f"  - {error['message']}")
    
    # 顯示配置摘要
    print("\n📋 配置摘要:")
    summary = Config.get_config_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    print("\n" + "=" * 50)
    
    # 給出建議
    if ptt_result['is_valid'] and cosmos_result['is_valid']:
        print("🎉 所有配置都正確！您可以啟動應用程式了。")
        print("\n🚀 下一步:")
        print("  1. 啟動應用程式: py app.py")
        print("  2. 開啟瀏覽器: http://localhost:5173")
        print("  3. 檢查系統配置頁面確認一切正常")
    else:
        print("⚠️  配置有問題，請檢查並修正：")
        if not ptt_result['is_valid']:
            print("  • PTT 帳號密碼設定")
        if not cosmos_result['is_valid']:
            print("  • Azure Cosmos DB 連接設定")
        print("\n🔧 修正方法:")
        print("  1. 編輯 .env 檔案")
        print("  2. 或重新執行 setup_env.py")
        print("  3. 確保所有必要欄位都已填入")


def show_azure_setup_guide():
    """顯示 Azure 設定指南"""
    print("\n☁️  Azure Cosmos DB 設定指南")
    print("=" * 40)
    print("1. 登入 Azure Portal: https://portal.azure.com")
    print("2. 搜尋並選擇您的 Cosmos DB 帳戶")
    print("3. 取得連接資訊:")
    print("   • 端點: 概觀 > Gremlin 端點")
    print("   • 金鑰: 金鑰 > 主要金鑰或次要金鑰")
    print("4. 確認資料庫和集合:")
    print("   • 資料庫: ptt_graph_db")
    print("   • 集合: ptt_graph")


if __name__ == "__main__":
    try:
        test_env_setup()
        
        # 詢問是否要顯示 Azure 設定指南
        print()
        show_guide = input("是否要顯示 Azure Cosmos DB 設定指南？(y/N): ").lower().strip()
        if show_guide == 'y':
            show_azure_setup_guide()
            
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
