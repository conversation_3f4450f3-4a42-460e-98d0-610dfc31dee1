#!/usr/bin/env python3
"""
測試 .augmentignore 保護效果
驗證敏感檔案是否被正確保護，不會被 Augment AI 讀取
"""

import os
import tempfile
import shutil
from pathlib import Path
import fnmatch


def load_augmentignore_patterns():
    """載入 .augmentignore 中的模式"""
    patterns = []
    augmentignore_file = Path('.augmentignore')
    
    if augmentignore_file.exists():
        with open(augmentignore_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    patterns.append(line)
    
    return patterns


def should_be_ignored(file_path, patterns):
    """檢查檔案是否應該被忽略"""
    file_path = str(file_path)
    
    for pattern in patterns:
        # 處理例外規則（以 ! 開頭）
        if pattern.startswith('!'):
            exception_pattern = pattern[1:]
            if fnmatch.fnmatch(file_path, exception_pattern):
                return False
        
        # 處理目錄模式（以 / 結尾）
        if pattern.endswith('/'):
            dir_pattern = pattern[:-1]
            if file_path.startswith(dir_pattern + '/') or file_path == dir_pattern:
                return True
        
        # 處理檔案模式
        if fnmatch.fnmatch(file_path, pattern):
            return True
        
        # 處理路徑中包含模式的情況
        if '/' in pattern:
            if fnmatch.fnmatch(file_path, pattern):
                return True
        else:
            # 檢查檔案名稱是否匹配模式
            filename = os.path.basename(file_path)
            if fnmatch.fnmatch(filename, pattern):
                return True
    
    return False


def create_test_files():
    """創建測試檔案來驗證保護效果"""
    test_files = [
        # 環境變數檔案
        '.env',
        '.env.local',
        '.env.production',
        'config.ini',
        'secrets.json',
        'credentials.json',
        
        # PTT 相關敏感檔案
        'ptt_credentials.txt',
        'ptt_config.yaml',
        'crawl_results/data.json',
        'user_data/profiles.json',
        'today_crawl_20250620.json',
        
        # 資料庫相關
        'cosmos_config.json',
        'connection_strings.txt',
        'backup/database.sql',
        'graph_data/network.json',
        
        # 日誌檔案
        'logs/system.log',
        'debug_output.txt',
        'error_report.txt',
        'ptt_system_20250620.log',
        
        # 測試資料
        'test_data/sample.json',
        'mock_data/users.json',
        'test_results/report.txt',
        'temp/scratch.txt',
        
        # 部署相關
        '.azure/config.json',
        'deployment_config.yaml',
        'publish_profile.pubxml',
        
        # 個人檔案
        'notes/personal.md',
        'private/confidential.txt',
        'my_secret_notes.txt',
        'api_key_config.json',
        
        # 加密檔案
        'private.key',
        'certificate.pem',
        'encrypted_data.enc',
        
        # 應該被保護的模式
        'user_password.txt',
        'secret_config.yaml',
        'my_credentials.json',
        'internal_document.pdf',
        
        # 不應該被保護的檔案
        'app.py',
        'config.py',
        'README.md',
        'requirements.txt',
        '.env.example'
    ]
    
    return test_files


def test_augmentignore_protection():
    """測試 .augmentignore 保護效果"""
    print("🔍 測試 .augmentignore 保護效果")
    print("=" * 50)
    
    # 載入保護模式
    patterns = load_augmentignore_patterns()
    print(f"📋 載入了 {len(patterns)} 個保護模式")
    
    # 創建測試檔案清單
    test_files = create_test_files()
    
    # 分類結果
    protected_files = []
    unprotected_files = []
    
    print("\n🔒 檢查檔案保護狀態:")
    print("-" * 30)
    
    for file_path in test_files:
        is_ignored = should_be_ignored(file_path, patterns)
        
        if is_ignored:
            protected_files.append(file_path)
            status = "🔒 受保護"
        else:
            unprotected_files.append(file_path)
            status = "🔓 未保護"
        
        print(f"{status}: {file_path}")
    
    # 顯示統計
    print(f"\n📊 保護統計:")
    print(f"  受保護檔案: {len(protected_files)}")
    print(f"  未保護檔案: {len(unprotected_files)}")
    print(f"  保護率: {len(protected_files) / len(test_files) * 100:.1f}%")
    
    # 檢查重要敏感檔案是否被保護
    critical_files = [
        '.env',
        'secrets.json',
        'ptt_credentials.txt',
        'cosmos_config.json',
        'private.key',
        'user_password.txt'
    ]
    
    print(f"\n🚨 關鍵敏感檔案檢查:")
    print("-" * 25)
    
    all_critical_protected = True
    for file_path in critical_files:
        is_protected = should_be_ignored(file_path, patterns)
        status = "✅ 已保護" if is_protected else "❌ 未保護"
        print(f"  {status}: {file_path}")
        
        if not is_protected:
            all_critical_protected = False
    
    # 檢查不應該被保護的檔案
    should_not_protect = [
        'app.py',
        'config.py', 
        'README.md',
        '.env.example'
    ]
    
    print(f"\n📂 重要程式檔案檢查:")
    print("-" * 20)
    
    for file_path in should_not_protect:
        is_protected = should_be_ignored(file_path, patterns)
        if is_protected:
            print(f"  ⚠️  意外保護: {file_path}")
        else:
            print(f"  ✅ 正常可見: {file_path}")
    
    # 總結
    print(f"\n" + "=" * 50)
    if all_critical_protected:
        print("🎉 所有關鍵敏感檔案都已受到保護！")
    else:
        print("⚠️  部分關鍵敏感檔案未受保護，請檢查 .augmentignore 設定")
    
    return {
        'total_files': len(test_files),
        'protected_files': len(protected_files),
        'unprotected_files': len(unprotected_files),
        'protection_rate': len(protected_files) / len(test_files) * 100,
        'all_critical_protected': all_critical_protected
    }


def show_protection_recommendations():
    """顯示保護建議"""
    print("\n💡 保護建議:")
    print("-" * 15)
    print("1. 定期檢查新增的敏感檔案")
    print("2. 更新 .augmentignore 以包含新的敏感模式")
    print("3. 避免在檔案名稱中包含敏感資訊")
    print("4. 使用環境變數而非硬編碼敏感資料")
    print("5. 定期審查和清理不需要的敏感檔案")


def create_sample_sensitive_files():
    """創建範例敏感檔案用於測試"""
    print("\n🧪 創建測試檔案:")
    print("-" * 15)
    
    test_files = {
        '.env.test': 'SECRET_KEY=test123\nPASSWORD=secret',
        'test_secret.txt': 'This is a secret file',
        'temp_credentials.json': '{"api_key": "test_key"}',
        'logs/test.log': 'Test log entry'
    }
    
    created_files = []
    
    for file_path, content in test_files.items():
        try:
            # 創建目錄（如果需要）
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 創建檔案
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            created_files.append(file_path)
            print(f"  ✅ 創建: {file_path}")
            
        except Exception as e:
            print(f"  ❌ 創建失敗 {file_path}: {e}")
    
    return created_files


def cleanup_test_files(files):
    """清理測試檔案"""
    print(f"\n🧹 清理測試檔案:")
    print("-" * 15)
    
    for file_path in files:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"  ✅ 刪除: {file_path}")
            
            # 清理空目錄
            dir_path = os.path.dirname(file_path)
            if dir_path and os.path.exists(dir_path) and not os.listdir(dir_path):
                os.rmdir(dir_path)
                print(f"  ✅ 刪除空目錄: {dir_path}")
                
        except Exception as e:
            print(f"  ❌ 刪除失敗 {file_path}: {e}")


if __name__ == "__main__":
    try:
        # 執行保護測試
        results = test_augmentignore_protection()
        
        # 顯示建議
        show_protection_recommendations()
        
        # 詢問是否要創建測試檔案
        print()
        create_test = input("是否要創建實際測試檔案來驗證保護效果？(y/N): ").lower().strip()
        
        if create_test == 'y':
            created_files = create_sample_sensitive_files()
            
            print("\n請檢查 Augment AI 是否能讀取這些測試檔案")
            print("如果無法讀取，表示保護機制正常運作")
            
            cleanup = input("\n是否要清理測試檔案？(Y/n): ").lower().strip()
            if cleanup != 'n':
                cleanup_test_files(created_files)
        
        print(f"\n🎯 測試完成！保護率: {results['protection_rate']:.1f}%")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
