# PTT 自動登入系統

這是一個基於 Flask 的 PTT 自動登入測試系統，可以自動連接並登入 PTT（批踢踢實業坊），主要用於測試登入功能和後續的自動化操作。

## 功能特色

- **PTT 自動登入**：使用 PyPtt 套件實現自動登入功能
- **Web 測試介面**：提供友善的網頁介面進行登入測試
- **詳細日誌記錄**：完整記錄操作過程和結果
- **錯誤處理機制**：自動重試和異常處理
- **配置管理**：支援環境變數配置
- **狀態監控**：即時查看系統和連接狀態

## 技術架構

- **後端框架**：Python Flask
- **PTT 連接**：PyPtt 套件
- **前端**：Bootstrap 5 + JavaScript
- **配置管理**：python-dotenv
- **日誌系統**：Python logging

## 快速開始

### 1. 環境準備

確保您的系統已安裝 Python 3.9 或更高版本。

### 2. 安裝依賴

```bash
# 建立虛擬環境
python -m venv .venv

# 啟動虛擬環境
# Windows
.venv\Scripts\activate
# Linux/MacOS
source .venv/bin/activate

# 安裝依賴套件
pip install -r requirements.txt
```

### 3. 配置設定

複製環境變數範例檔案並設定您的 PTT 帳號資訊：

```bash
cp .env.example .env
```

編輯 `.env` 檔案，填入您的 PTT 帳號和密碼：

```env
PTT_USERNAME=your_ptt_username
PTT_PASSWORD=your_ptt_password
```

### 4. 執行應用程式

```bash
python app.py
```

### 5. 開啟測試介面

在瀏覽器中開啟 `http://localhost:5000`，即可看到 PTT 登入測試介面。

## 使用說明

### Web 介面功能

1. **系統配置顯示**：查看當前的系統配置（隱藏敏感資訊）
2. **PTT 登入測試**：
   - 可以使用環境變數中的帳密
   - 也可以在表單中臨時輸入帳密進行測試
3. **測試結果顯示**：詳細顯示登入過程和結果
4. **系統狀態監控**：查看 PTT 客戶端和配置狀態

### API 端點

- `GET /`：主頁面
- `POST /test-login`：執行 PTT 登入測試
- `GET /status`：取得系統狀態
- `GET /health`：健康檢查

## 配置選項

所有配置都可以透過環境變數設定：

| 環境變數 | 說明 | 預設值 |
|---------|------|--------|
| `PTT_USERNAME` | PTT 帳號 | 無 |
| `PTT_PASSWORD` | PTT 密碼 | 無 |
| `PTT_HOST` | PTT 主機 | ptt.cc |
| `PTT_PORT` | PTT 連接埠 | 23 |
| `LOG_LEVEL` | 日誌等級 | INFO |
| `TIMEZONE` | 時區 | Asia/Taipei |
| `MAX_RETRY_ATTEMPTS` | 最大重試次數 | 3 |
| `CONNECTION_TIMEOUT` | 連接超時(秒) | 30 |

## 日誌記錄

系統會自動建立 `logs` 目錄並記錄詳細的操作日誌：

- 控制台輸出：即時顯示重要訊息
- 檔案日誌：按日期儲存完整的操作記錄

## 部署到 Azure

此專案已預先配置好 Azure 部署相關檔案：

1. `azure.yaml`：Azure 部署配置
2. `infra/`：基礎設施即程式碼 (Bicep)

詳細部署說明請參考 [SAdoc.md](./SAdoc.md)。

## 安全注意事項

- 請勿將 `.env` 檔案提交到版本控制系統
- 生產環境建議使用 Azure Key Vault 儲存敏感資訊
- 定期更換 PTT 帳號密碼

## 故障排除

### 常見問題

1. **連接失敗**：檢查網路連接和 PTT 伺服器狀態
2. **登入失敗**：確認帳號密碼正確，檢查是否有其他連線
3. **套件安裝失敗**：確保 Python 版本符合需求

### 日誌查看

查看 `logs/` 目錄下的日誌檔案，獲取詳細的錯誤資訊。

## 開發說明

### 專案結構

```
├── app.py              # 主應用程式
├── config.py           # 配置管理
├── ptt_client.py       # PTT 客戶端
├── logger.py           # 日誌記錄
├── requirements.txt    # 依賴套件
├── .env.example        # 環境變數範例
├── templates/          # HTML 模板
├── static/            # 靜態檔案
├── logs/              # 日誌檔案 (自動建立)
└── infra/             # Azure 基礎設施
```

### 擴展功能

系統設計為模組化架構，可以輕鬆擴展以下功能：

- 自動發文功能
- 排程任務
- 多帳號管理
- 更多 PTT 操作

## 授權

此專案採用 MIT 授權條款。
