#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試看板存取權限
"""

import sys
from ptt_client import PTTClient

# 設定正確的編碼
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')

def test_board_access(board_name):
    """測試看板存取權限"""
    print("=" * 60)
    print(f"測試看板存取權限: {board_name}")
    print("=" * 60)
    
    try:
        # 建立 PTT 客戶端
        client = PTTClient()
        
        # 登入 PTT
        print("1. 登入 PTT...")
        if not client.login():
            print("❌ PTT 登入失敗")
            return False
        
        print("✅ PTT 登入成功")
        
        # 測試看板資訊
        print(f"\n2. 測試看板 '{board_name}' 的存取權限...")
        
        try:
            # 使用 PyPtt 直接測試看板
            board_info = client.ptt_bot.get_board_info(board_name)
            
            if board_info:
                print(f"✅ 看板 '{board_name}' 存在且可存取")
                print(f"   看板資訊: {board_info}")
                
                # 嘗試取得最新索引
                try:
                    newest_index = client.ptt_bot.get_newest_index(board_name)
                    print(f"   最新文章索引: {newest_index}")
                    
                    if newest_index and newest_index > 0:
                        print(f"   看板有 {newest_index} 篇文章")
                        
                        # 嘗試取得最新幾篇文章
                        print(f"\n3. 嘗試取得最新 3 篇文章...")
                        posts = client.get_board_posts(board_name, max_posts=3)
                        
                        if posts:
                            print(f"✅ 成功取得 {len(posts)} 篇文章")
                            for i, post in enumerate(posts):
                                print(f"   文章 {i+1}: {post.get('title', 'N/A')} by {post.get('author', 'N/A')}")
                        else:
                            print("❌ 無法取得文章列表")
                    else:
                        print("   看板沒有文章或索引為 0")
                        
                except Exception as e:
                    print(f"❌ 取得文章索引失敗: {e}")
                    
            else:
                print(f"❌ 看板 '{board_name}' 不存在或無法存取")
                return False
                
        except Exception as e:
            print(f"❌ 存取看板 '{board_name}' 失敗: {e}")
            
            # 檢查是否是權限問題
            if "權限不足" in str(e) or "permission" in str(e).lower():
                print("   可能原因: 權限不足，需要更高等級的帳號")
            elif "不存在" in str(e) or "not found" in str(e).lower():
                print("   可能原因: 看板不存在或已關閉")
            else:
                print(f"   錯誤詳情: {e}")
            
            return False
        
        # 登出
        print("\n4. 登出 PTT...")
        client.logout()
        print("✅ PTT 登出成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_boards():
    """測試多個看板"""
    print("\n" + "=" * 60)
    print("測試多個看板的存取權限")
    print("=" * 60)
    
    # 測試不同類型的看板
    test_boards = [
        "Test",        # 測試看板（通常可存取）
        "Gossiping",   # 八卦板（需要註冊滿一定時間）
        "Stock",       # 股票板（正確的大小寫）
        "stock",       # 小寫版本
        "Baseball",    # 棒球板
        "movie",       # 電影板
        "Tech_Job",    # 科技工作板
        "Soft_Job"     # 軟體工作板
    ]
    
    results = {}
    
    for board in test_boards:
        print(f"\n--- 測試看板: {board} ---")
        success = test_board_access(board)
        results[board] = success
        
        # 避免過於頻繁的請求
        import time
        time.sleep(2)
    
    # 顯示結果摘要
    print("\n" + "=" * 60)
    print("測試結果摘要")
    print("=" * 60)
    
    for board, success in results.items():
        status = "✅ 可存取" if success else "❌ 無法存取"
        print(f"{board:15} - {status}")

def suggest_alternative_boards():
    """建議替代的看板"""
    print("\n" + "=" * 60)
    print("建議的替代看板")
    print("=" * 60)
    
    suggestions = [
        ("Test", "測試看板 - 通常開放給所有使用者"),
        ("joke", "笑話板 - 通常容易存取"),
        ("Beauty", "表特板 - 熱門板但可能需要權限"),
        ("movie", "電影板 - 通常開放"),
        ("Baseball", "棒球板 - 熱門運動板"),
        ("NBA", "NBA板 - 籃球討論"),
        ("Tech_Job", "科技工作板 - 求職相關"),
        ("Soft_Job", "軟體工作板 - 程式設計工作")
    ]
    
    print("推薦測試的看板:")
    for board, description in suggestions:
        print(f"  {board:12} - {description}")

if __name__ == "__main__":
    # 首先測試 stock 看板
    print("首先測試您嘗試的 stock 看板...")
    test_board_access("stock")
    
    # 也測試大寫版本
    print("\n" + "="*40)
    print("測試大寫版本 Stock...")
    test_board_access("Stock")
    
    # 建議替代看板
    suggest_alternative_boards()
    
    # 如果需要，可以測試多個看板
    user_input = input("\n是否要測試多個看板的存取權限？(y/n): ")
    if user_input.lower() == 'y':
        test_multiple_boards()
