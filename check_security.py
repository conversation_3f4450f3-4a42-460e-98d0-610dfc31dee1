#!/usr/bin/env python3
"""
PTT 圖形分析系統 - 安全性檢查腳本
檢查是否有敏感資料洩露的風險
"""

import os
import re
import sys
from pathlib import Path

def check_env_file():
    """檢查 .env 文件是否包含預設值"""
    env_file = Path('.env')
    if not env_file.exists():
        print("❌ .env 文件不存在，請從 .env.example 複製並設定")
        return False
    
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 檢查是否還有預設值
    issues = []
    
    if 'your_ptt_username' in content:
        issues.append("PTT_USERNAME 還是預設值")
    
    if 'your_ptt_password' in content:
        issues.append("PTT_PASSWORD 還是預設值")
    
    if 'your-cosmos-account' in content:
        issues.append("COSMOS_DB_ENDPOINT 還是預設值")
    
    if 'your_cosmos_db_primary_key' in content:
        issues.append("COSMOS_DB_KEY 還是預設值")
    
    if 'your-production-secret-key-here' in content:
        issues.append("SECRET_KEY 還是預設值")
    
    if issues:
        print("❌ .env 文件配置問題：")
        for issue in issues:
            print(f"   - {issue}")
        return False
    else:
        print("✅ .env 文件配置正確")
        return True

def check_code_files():
    """檢查程式碼文件是否包含硬編碼的敏感資料"""
    python_files = list(Path('.').glob('**/*.py'))
    issues = []
    
    # 敏感資料模式
    patterns = [
        (r'password\s*=\s*["\'][^"\']{3,}["\']', '可能的硬編碼密碼'),
        (r'key\s*=\s*["\'][A-Za-z0-9+/]{20,}["\']', '可能的硬編碼金鑰'),
        (r'wss://[a-zA-Z0-9.-]+\.gremlin\.cosmos\.azure\.com', '硬編碼的 Cosmos DB 端點'),
        (r'[A-Za-z0-9+/]{60,}==', '可能的 Base64 編碼金鑰'),
    ]
    
    for py_file in python_files:
        if py_file.name == 'check_security.py':
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for pattern, description in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    issues.append(f"{py_file}: {description}")
        except Exception as e:
            print(f"⚠️  無法讀取 {py_file}: {e}")
    
    if issues:
        print("❌ 程式碼中發現可能的敏感資料：")
        for issue in issues:
            print(f"   - {issue}")
        return False
    else:
        print("✅ 程式碼文件檢查通過")
        return True

def check_gitignore():
    """檢查 .gitignore 是否正確設定"""
    gitignore_file = Path('.gitignore')
    if not gitignore_file.exists():
        print("❌ .gitignore 文件不存在")
        return False
    
    with open(gitignore_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_entries = ['.env', '*.log', 'logs/']
    missing = []
    
    for entry in required_entries:
        if entry not in content:
            missing.append(entry)
    
    if missing:
        print("❌ .gitignore 缺少必要項目：")
        for item in missing:
            print(f"   - {item}")
        return False
    else:
        print("✅ .gitignore 設定正確")
        return True

def check_environment_variables():
    """檢查環境變數是否已設定"""
    required_vars = [
        'PTT_USERNAME',
        'PTT_PASSWORD', 
        'COSMOS_DB_ENDPOINT',
        'COSMOS_DB_KEY',
        'SECRET_KEY'
    ]
    
    missing = []
    for var in required_vars:
        if not os.getenv(var):
            missing.append(var)
    
    if missing:
        print("⚠️  以下環境變數未設定（部署時需要）：")
        for var in missing:
            print(f"   - {var}")
        return False
    else:
        print("✅ 所有必要環境變數已設定")
        return True

def main():
    """主要檢查函數"""
    print("🔒 PTT 圖形分析系統 - 安全性檢查")
    print("=" * 50)
    
    checks = [
        ("檢查 .env 文件", check_env_file),
        ("檢查程式碼文件", check_code_files),
        ("檢查 .gitignore", check_gitignore),
        ("檢查環境變數", check_environment_variables),
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}...")
        if not check_func():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有安全性檢查通過！")
        print("💡 建議：")
        print("   - 定期更換密碼和金鑰")
        print("   - 在生產環境使用 Azure Key Vault")
        print("   - 監控系統存取日誌")
        return 0
    else:
        print("⚠️  發現安全性問題，請修正後再部署")
        print("📖 詳細設定指引請參考 CONFIGURATION_GUIDE.md")
        return 1

if __name__ == "__main__":
    sys.exit(main())
