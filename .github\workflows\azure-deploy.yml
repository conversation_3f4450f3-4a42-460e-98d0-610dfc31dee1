name: Deploy to Azure Web App

on:
  push:
    branches:
      - main
  workflow_dispatch:

env:
  AZURE_WEBAPP_NAME: TwLotteryBot
  AZURE_RESOURCE_GROUP: TwLotteryBot_group
  PYTHON_VERSION: '3.12'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python version
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Create and start virtual environment
        run: |
          python -m venv venv
          source venv/bin/activate

      - name: Install dependencies
        run: |
          source venv/bin/activate
          pip install --upgrade pip
          pip install -r requirements.txt

      - name: Run tests
        run: |
          source venv/bin/activate
          python -c "import app; print('App import successful')"
          python test_crawler.py || echo "Crawler tests skipped (no PTT credentials)"

      - name: Build frontend
        run: |
          cd frontend
          npm ci
          npm run build

      - name: Zip artifact for deployment
        run: |
          zip -r release.zip . \
            -x "*.git*" "*venv*" "*__pycache__*" "*.pyc" "*test_*" "*.md" ".env" \
            "frontend/node_modules/*"

      - name: Upload artifact for deployment jobs
        uses: actions/upload-artifact@v4
        with:
          name: python-app
          path: release.zip

  deploy:
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: 'Production'
      url: https://${{ env.AZURE_WEBAPP_NAME }}.azurewebsites.net

    steps:
      - name: Download artifact from build job
        uses: actions/download-artifact@v4
        with:
          name: python-app

      - name: Unzip artifact for deployment
        run: unzip release.zip

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: 'Deploy to Azure Web App'
        uses: azure/webapps-deploy@v2
        id: deploy-to-webapp
        with:
          app-name: ${{ env.AZURE_WEBAPP_NAME }}
          package: .

      - name: Test deployment
        run: |
          sleep 20
          curl -f https://${{ env.AZURE_WEBAPP_NAME }}.azurewebsites.net/api/system/health || echo "Health check failed"
