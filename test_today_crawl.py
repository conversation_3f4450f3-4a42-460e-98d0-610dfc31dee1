#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試抓取今天的文章
"""

import sys
from datetime import datetime
from crawler import PTTCrawler
from graph_writer import GraphWriter

# 設定正確的編碼
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')

def test_today_crawl():
    """測試抓取今天的文章"""
    print("=" * 60)
    print("測試抓取今天的文章")
    print("=" * 60)
    
    # 取得今天的日期
    today = datetime.now().strftime('%Y-%m-%d')
    print(f"目標日期: {today}")
    
    try:
        # 建立爬蟲和圖形寫入器
        crawler = PTTCrawler()
        writer = GraphWriter()
        
        # 設定要爬取的看板
        boards = ['Test']  # 先從 Test 看板開始測試
        max_posts = 20     # 每個看板最多 20 篇文章
        
        print(f"開始爬取看板: {boards}")
        print(f"每個看板最多文章數: {max_posts}")
        print(f"目標日期: {today}")
        
        # 爬取今天的文章
        print("\n1. 開始爬取文章...")
        result = crawler.crawl_boards(
            board_list=boards,
            max_posts_per_board=max_posts,
            target_date=today
        )
        
        if not result['success']:
            print("❌ 爬取失敗")
            return False
        
        print(f"✅ 爬取成功!")
        print(f"   爬取時間: {result['crawl_time']}")
        print(f"   目標日期: {result['target_date']}")
        print(f"   看板數量: {result['summary']['boards_count']}")
        print(f"   總文章數: {result['summary']['total_posts']}")
        print(f"   總使用者數: {result['summary']['total_users']}")
        print(f"   總IP數: {result['summary']['total_ips']}")
        
        # 顯示詳細資訊
        for board_data in result['boards']:
            print(f"\n看板 {board_data['board']}:")
            print(f"   文章數: {board_data['posts_count']}")
            print(f"   使用者數: {len(board_data['users'])}")
            print(f"   IP數: {len(board_data['ips'])}")
            
            # 顯示前幾篇文章
            for i, post in enumerate(board_data['posts'][:3]):
                print(f"   文章 {i+1}: {post['title']} (作者: {post['author']}, 日期: {post['date']})")
        
        # 如果沒有找到今天的文章，嘗試不指定日期
        if result['summary']['total_posts'] == 0:
            print(f"\n⚠️ 沒有找到 {today} 的文章，嘗試抓取最新文章...")
            
            result = crawler.crawl_boards(
                board_list=boards,
                max_posts_per_board=5  # 減少數量
            )
            
            if result['success'] and result['summary']['total_posts'] > 0:
                print(f"✅ 找到 {result['summary']['total_posts']} 篇最新文章")
                
                # 顯示文章日期
                for board_data in result['boards']:
                    print(f"\n看板 {board_data['board']} 的文章日期:")
                    for post in board_data['posts']:
                        print(f"   {post['title']} - 日期: {post['date']}")
            else:
                print("❌ 也無法取得最新文章")
                return False
        
        # 轉換為圖資料格式
        print("\n2. 轉換為圖資料格式...")
        graph_data = crawler.generate_graph_data(result)
        
        vertex_count = (len(graph_data['vertices']['users']) + 
                       len(graph_data['vertices']['ips']) + 
                       len(graph_data['vertices']['posts']))
        
        edge_count = (len(graph_data['edges']['posted']) + 
                     len(graph_data['edges']['commented']) + 
                     len(graph_data['edges']['used_ip']) + 
                     len(graph_data['edges']['from_ip']))
        
        print(f"✅ 圖資料轉換完成")
        print(f"   頂點數: {vertex_count}")
        print(f"   邊數: {edge_count}")
        print(f"   使用者頂點: {len(graph_data['vertices']['users'])}")
        print(f"   IP頂點: {len(graph_data['vertices']['ips'])}")
        print(f"   文章頂點: {len(graph_data['vertices']['posts'])}")
        
        # 寫入 Cosmos DB（使用增量更新）
        print("\n3. 寫入 Cosmos DB（增量更新模式）...")
        write_result = writer.write_graph_data(graph_data, incremental=True)

        if write_result:
            print("✅ 成功寫入 Cosmos DB（支援重複檢查和增量更新）")
        else:
            print("❌ 寫入 Cosmos DB 失敗")
            return False
        
        # 儲存爬取結果
        print("\n4. 儲存爬取結果...")
        filename = crawler.save_crawl_result(result, f"today_crawl_{today.replace('-', '')}.json")
        print(f"✅ 結果已儲存至: {filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_date_parsing():
    """測試日期解析功能"""
    print("\n" + "=" * 60)
    print("測試日期解析功能")
    print("=" * 60)
    
    from ptt_client import PTTClient
    client = PTTClient()
    
    # 測試不同的日期格式
    test_dates = [
        ("6/19", "2025-06-19"),
        ("12/25", "2025-12-25"),
        ("2025-06-19", "2025-06-19"),
        ("2025-06-19 10:30:45", "2025-06-19"),
        ("Jun 19", "2025-06-19"),  # 這個可能無法解析
        ("", "2025-06-19"),  # 空字串
    ]
    
    for post_date, target_date in test_dates:
        result = client._is_date_match(post_date, target_date)
        print(f"文章日期: '{post_date}' vs 目標日期: '{target_date}' -> {result}")

if __name__ == "__main__":
    # 測試日期解析
    test_date_parsing()
    
    # 測試今天的文章爬取
    success = test_today_crawl()
    
    if success:
        print("\n🎉 今天文章爬取測試成功！")
    else:
        print("\n❌ 今天文章爬取測試失敗")
