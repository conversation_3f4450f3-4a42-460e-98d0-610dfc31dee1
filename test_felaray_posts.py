#!/usr/bin/env python3
"""
專門測試 felaray 在 Test 看板的文章推文
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from ptt_client import PTTClient

def test_felaray_posts():
    """測試 felaray 的文章推文"""
    print("=" * 60)
    print("測試 felaray 在 Test 看板的文章推文")
    print("=" * 60)
    
    # 檢查配置
    config_errors = Config.validate_config()
    if config_errors:
        print("❌ 配置錯誤:")
        for error in config_errors:
            print(f"   - {error}")
        return False
    
    print(f"✅ 使用帳號: {Config.PTT_USERNAME}")
    
    # 建立 PTT 客戶端
    client = PTTClient()
    
    try:
        # 1. 測試登入
        print("\n1. 測試 PTT 登入...")
        if not client.login():
            print("❌ PTT 登入失敗")
            return False
        print("✅ PTT 登入成功")
        
        # 2. 取得 Test 看板的更多文章
        print("\n2. 取得 Test 看板文章列表...")
        board_name = "Test"
        posts = client.get_board_posts(board_name, max_posts=20)  # 增加文章數量
        
        if not posts:
            print("❌ 無法取得文章列表")
            return False
        
        print(f"✅ 成功取得 {len(posts)} 篇文章")
        
        # 3. 尋找 felaray 的文章
        print("\n3. 尋找 felaray 的文章...")
        felaray_posts = []
        for post in posts:
            author = post.get('author', '')
            if 'felaray' in author.lower():
                felaray_posts.append(post)
                print(f"找到 felaray 文章: {post.get('title', 'N/A')}")
        
        if not felaray_posts:
            print("⚠️ 未找到 felaray 的文章")
            print("顯示所有文章作者:")
            for i, post in enumerate(posts):
                print(f"  {i+1}. 作者: {post.get('author', 'N/A')} - 標題: {post.get('title', 'N/A')}")
            return False
        
        print(f"✅ 找到 {len(felaray_posts)} 篇 felaray 的文章")
        
        # 4. 詳細檢查每篇 felaray 的文章
        print("\n4. 詳細檢查 felaray 的文章推文...")
        
        for i, post in enumerate(felaray_posts):
            print(f"\n--- felaray 文章 {i+1} ---")
            print(f"標題: {post.get('title', 'N/A')}")
            print(f"作者: {post.get('author', 'N/A')}")
            print(f"日期: {post.get('date', 'N/A')}")
            print(f"AID: {post.get('aid', 'N/A')}")
            
            if not post.get('aid'):
                print("⚠️ 文章沒有 AID，跳過")
                continue
            
            # 取得文章詳細內容
            detailed_content = client.get_post_content(post['aid'], board_name)
            
            if detailed_content:
                print("✅ 成功取得文章詳細內容")
                
                # 檢查文章內容
                content = detailed_content.get('content', '')
                if content:
                    print(f"📄 文章內容長度: {len(content)} 字元")
                    # 顯示前200字元
                    preview = content[:200].replace('\n', ' ')
                    print(f"📄 內容預覽: {preview}...")
                else:
                    print("⚠️ 文章內容為空")
                
                # 檢查推文
                comments = detailed_content.get('comments', [])
                if comments:
                    print(f"💬 找到 {len(comments)} 則推文！")
                    
                    # 顯示所有推文的詳細資訊
                    for j, comment in enumerate(comments):
                        print(f"  推文 {j+1}:")
                        print(f"    類型: '{comment.get('type', 'N/A')}'")
                        print(f"    作者: '{comment.get('author', 'N/A')}'")
                        print(f"    內容: '{comment.get('content', 'N/A')}'")
                        print(f"    IP: '{comment.get('ip', 'N/A')}'")
                        print(f"    時間: '{comment.get('time', 'N/A')}'")
                        print(f"    原始資料: {comment}")
                    
                    # 統計推文類型
                    comment_types = {}
                    for comment in comments:
                        comment_type = comment.get('type', '未知')
                        comment_types[comment_type] = comment_types.get(comment_type, 0) + 1
                    
                    print(f"  推文類型統計: {comment_types}")
                    
                    return True  # 找到推文就返回成功
                    
                else:
                    print("📭 此文章沒有推文")
                
                # 檢查 IP 資訊
                ip = detailed_content.get('ip', '')
                if ip:
                    print(f"🌐 發文者 IP: {ip}")
                else:
                    print("🌐 無發文者 IP 資訊")
                
            else:
                print("❌ 無法取得文章詳細內容")
            
            # 避免過於頻繁的請求
            time.sleep(1)
        
        print("\n⚠️ 檢查完所有 felaray 的文章，但都沒有找到推文")
        return False
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 登出
        try:
            if client.is_logged_in:
                client.logout()
                print("\n✅ 已登出 PTT")
        except:
            pass

def test_direct_pyptt_felaray():
    """直接使用 PyPtt 測試 felaray 的文章"""
    print("\n" + "=" * 60)
    print("直接使用 PyPtt 測試 felaray 的文章")
    print("=" * 60)
    
    try:
        import PyPtt
        
        # 建立 PyPtt 物件
        ptt_bot = PyPtt.API()
        
        print("1. 登入 PTT...")
        ptt_bot.login(
            ptt_id=Config.PTT_USERNAME,
            ptt_pw=Config.PTT_PASSWORD,
            kick_other_session=True
        )
        print("✅ 登入成功")
        
        # 測試取得文章
        print("\n2. 測試取得 Test 看板文章...")
        board_name = "Test"
        
        # 取得最新索引
        newest_index = ptt_bot.get_newest_index(
            index_type=PyPtt.NewIndex.BOARD,
            board=board_name
        )
        print(f"✅ 最新索引: {newest_index}")
        
        # 測試更多文章
        felaray_found = False
        for i in range(max(1, newest_index - 30), newest_index + 1):  # 檢查更多文章
            try:
                post = ptt_bot.get_post(
                    board=board_name,
                    index=i
                )
                
                if post:
                    # 檢查作者
                    author = ""
                    if isinstance(post, dict):
                        author = post.get('author', '') or post.get('Author', '')
                    else:
                        author = getattr(post, 'author', '') or getattr(post, 'Author', '')
                    
                    if 'felaray' in author.lower():
                        print(f"\n--- 找到 felaray 文章 (索引 {i}) ---")
                        felaray_found = True
                        
                        print(f"文章類型: {type(post)}")
                        
                        if isinstance(post, dict):
                            print("文章字典內容:")
                            for key, value in post.items():
                                print(f"  {key}: {value}")
                        else:
                            print("文章物件屬性:")
                            for attr in dir(post):
                                if not attr.startswith('_') and not callable(getattr(post, attr)):
                                    try:
                                        value = getattr(post, attr)
                                        print(f"  {attr}: {value}")
                                    except:
                                        print(f"  {attr}: 無法取得值")
                        
                        # 檢查推文相關屬性
                        comment_attrs = ['comments', 'pushes', 'responses', 'replies', 'push_list']
                        found_comments = False
                        for attr in comment_attrs:
                            value = None
                            if isinstance(post, dict):
                                value = post.get(attr)
                            else:
                                value = getattr(post, attr, None)
                            
                            if value is not None:
                                print(f"💬 {attr}: {value} (類型: {type(value)})")
                                if isinstance(value, list) and value:
                                    print(f"   列表長度: {len(value)}")
                                    for j, item in enumerate(value[:3]):
                                        print(f"     項目 {j+1}: {item}")
                                    found_comments = True
                        
                        if found_comments:
                            return True
                        
                        break  # 找到 felaray 的文章就停止
                        
            except Exception as e:
                continue  # 忽略單篇文章的錯誤
        
        if not felaray_found:
            print("⚠️ 未找到 felaray 的文章")
        
        return felaray_found
        
    except Exception as e:
        print(f"❌ 直接 PyPtt 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("開始測試 felaray 的文章推文...")
    
    # 測試 1: 使用 PTTClient
    success1 = test_felaray_posts()
    
    # 測試 2: 直接使用 PyPtt
    success2 = test_direct_pyptt_felaray()
    
    print("\n" + "=" * 60)
    print("最終測試結果")
    print("=" * 60)
    
    if success1:
        print("✅ PTTClient 找到 felaray 的推文")
    else:
        print("❌ PTTClient 未找到 felaray 的推文")
    
    if success2:
        print("✅ 直接 PyPtt 找到 felaray 的推文")
    else:
        print("❌ 直接 PyPtt 未找到 felaray 的推文")
    
    if success1 or success2:
        print("\n🎉 成功找到推文！PTT 推文功能正常")
    else:
        print("\n⚠️ 未找到推文，可能需要檢查推文抓取邏輯")
