"""
PTT 自動登入系統 - 圖形查詢 API 路由
"""

from flask import request, jsonify, current_app, Response
from . import api_bp
from graph_query import GraphQuery
from graph_query_simple import SimpleGraphQuery
from logger import ptt_logger
import json

def json_response(data, status_code=200):
    """建立正確編碼的 JSON 回應"""
    return Response(
        json.dumps(data, ensure_ascii=False, indent=2),
        status=status_code,
        mimetype='application/json; charset=utf-8'
    )

# 建立全域圖形查詢器實例
graph_query = GraphQuery()
simple_graph_query = SimpleGraphQuery()



@api_bp.route('/ip-users', methods=['GET'])
def get_ip_users():
    """查詢使用過該 IP 的帳號
    
    Query Parameters:
        ip (str): IP 位址
    
    Returns:
        JSON: IP 使用者資料
    """
    try:
        ip_address = request.args.get('ip')
        
        if not ip_address:
            return jsonify({
                'success': False,
                'error': '缺少必要參數: ip'
            }), 400
        
        # 簡單的 IP 格式驗證
        import re
        ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if not re.match(ip_pattern, ip_address):
            return jsonify({
                'success': False,
                'error': 'IP 位址格式不正確'
            }), 400
        
        ptt_logger.info(f"API 請求: 查詢 IP 使用者 - {ip_address}")
        
        # 執行查詢
        result = graph_query.query_ip_users(ip_address)
        
        if 'error' in result:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
        
        return jsonify({
            'success': True,
            'data': result,
            'query_info': {
                'ip': ip_address,
                'timestamp': ptt_logger.get_timestamp()
            }
        })
        
    except Exception as e:
        ptt_logger.error(f"查詢 IP 使用者 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/post-interactions', methods=['GET'])
def get_post_interactions():
    """查詢該文互動網絡
    
    Query Parameters:
        post_id (str): 文章 ID
    
    Returns:
        JSON: 文章互動資料
    """
    try:
        post_id = request.args.get('post_id')
        
        if not post_id:
            return jsonify({
                'success': False,
                'error': '缺少必要參數: post_id'
            }), 400
        
        ptt_logger.info(f"API 請求: 查詢文章互動 - {post_id}")
        
        # 執行查詢
        result = graph_query.query_post_interactions(post_id)
        
        if 'error' in result:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
        
        return jsonify({
            'success': True,
            'data': result,
            'query_info': {
                'post_id': post_id,
                'timestamp': ptt_logger.get_timestamp()
            }
        })
        
    except Exception as e:
        ptt_logger.error(f"查詢文章互動 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/search-users', methods=['GET'])
def search_users():
    """搜尋使用者
    
    Query Parameters:
        pattern (str): 搜尋模式
        limit (int, optional): 結果限制，預設20
    
    Returns:
        JSON: 使用者列表
    """
    try:
        pattern = request.args.get('pattern')
        limit = int(request.args.get('limit', 20))
        
        if not pattern:
            return jsonify({
                'success': False,
                'error': '缺少必要參數: pattern'
            }), 400
        
        if limit > 100:  # 限制最大查詢數量
            limit = 100
        
        ptt_logger.info(f"API 請求: 搜尋使用者 - {pattern}")
        
        # 執行查詢
        result = graph_query.search_users_by_pattern(pattern, limit)
        
        return jsonify({
            'success': True,
            'data': {
                'users': result,
                'count': len(result)
            },
            'query_info': {
                'pattern': pattern,
                'limit': limit,
                'timestamp': ptt_logger.get_timestamp()
            }
        })
        
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': f'參數格式錯誤: {str(e)}'
        }), 400
    except Exception as e:
        ptt_logger.error(f"搜尋使用者 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/popular-boards', methods=['GET'])
def get_popular_boards():
    """取得熱門看板
    
    Query Parameters:
        limit (int, optional): 結果限制，預設10
    
    Returns:
        JSON: 看板列表
    """
    try:
        limit = int(request.args.get('limit', 10))
        
        if limit > 50:  # 限制最大查詢數量
            limit = 50
        
        ptt_logger.info(f"API 請求: 取得熱門看板 - limit: {limit}")
        
        # 執行查詢
        result = graph_query.get_popular_boards(limit)
        
        return jsonify({
            'success': True,
            'data': {
                'boards': result,
                'count': len(result)
            },
            'query_info': {
                'limit': limit,
                'timestamp': ptt_logger.get_timestamp()
            }
        })
        
    except ValueError as e:
        return jsonify({
            'success': False,
            'error': f'參數格式錯誤: {str(e)}'
        }), 400
    except Exception as e:
        ptt_logger.error(f"取得熱門看板 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500



@api_bp.route('/users', methods=['GET'])
def list_all_users():
    """列出所有使用者

    Query Parameters:
        limit (int, optional): 結果限制，預設100
        offset (int, optional): 偏移量，預設0

    Returns:
        JSON: 使用者列表
    """
    try:
        limit = int(request.args.get('limit', 100))
        offset = int(request.args.get('offset', 0))

        if limit > 500:  # 限制最大查詢數量
            limit = 500

        ptt_logger.info(f"API 請求: 列出所有使用者 (limit={limit}, offset={offset})")

        # 執行查詢
        result = graph_query.get_all_users(limit, offset)

        return json_response({
            'success': True,
            'data': {
                'users': result,
                'count': len(result),
                'limit': limit,
                'offset': offset
            },
            'query_info': {
                'timestamp': ptt_logger.get_timestamp()
            }
        })

    except ValueError as e:
        return jsonify({
            'success': False,
            'error': f'參數格式錯誤: {str(e)}'
        }), 400
    except Exception as e:
        ptt_logger.error(f"列出使用者 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/ips', methods=['GET'])
def list_all_ips():
    """列出所有 IP 位址

    Query Parameters:
        limit (int, optional): 結果限制，預設100
        offset (int, optional): 偏移量，預設0

    Returns:
        JSON: IP 位址列表
    """
    try:
        limit = int(request.args.get('limit', 100))
        offset = int(request.args.get('offset', 0))

        if limit > 500:  # 限制最大查詢數量
            limit = 500

        ptt_logger.info(f"API 請求: 列出所有 IP (limit={limit}, offset={offset})")

        # 執行查詢
        result = graph_query.get_all_ips(limit, offset)

        return json_response({
            'success': True,
            'data': {
                'ips': result,
                'count': len(result),
                'limit': limit,
                'offset': offset
            },
            'query_info': {
                'timestamp': ptt_logger.get_timestamp()
            }
        })

    except ValueError as e:
        return jsonify({
            'success': False,
            'error': f'參數格式錯誤: {str(e)}'
        }), 400
    except Exception as e:
        ptt_logger.error(f"列出 IP API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/user-ip-analysis', methods=['GET'])
def analyze_user_ip():
    """分析使用者的 IP 使用情況和相同 IP 的其他使用者

    Query Parameters:
        username (str): 要分析的使用者名稱

    Returns:
        JSON: IP 分析結果
    """
    try:
        username = request.args.get('username')
        if not username:
            return jsonify({
                'success': False,
                'error': '請提供 username 參數'
            }), 400

        ptt_logger.info(f"API 請求: 分析使用者 IP - {username}")

        # 執行查詢
        result = graph_query.analyze_user_ip_relationships(username)

        return json_response({
            'success': True,
            'data': result,
            'query_info': {
                'username': username,
                'timestamp': ptt_logger.get_timestamp()
            }
        })

    except Exception as e:
        ptt_logger.error(f"分析使用者 IP API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/posts', methods=['GET'])
def get_posts():
    """取得文章列表及其發文者和推文者資訊

    Query Parameters:
        limit (int, optional): 結果限制，預設 20，最大 100
        offset (int, optional): 偏移量，預設 0
        board (str, optional): 看板篩選

    Returns:
        JSON: 文章列表資料
    """
    try:
        # 取得查詢參數
        limit = min(int(request.args.get('limit', 20)), 100)  # 限制最大100
        offset = max(int(request.args.get('offset', 0)), 0)   # 確保非負數
        board = request.args.get('board', '').strip()

        # 看板名稱大小寫不敏感處理
        if board:
            board = board.lower()

        ptt_logger.info(f"API 請求: 取得文章列表 - limit={limit}, offset={offset}, board={board}")

        # 執行查詢
        result = graph_query.get_all_posts(limit=limit, offset=offset, board=board)

        if 'error' in result:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500

        return json_response({
            'success': True,
            'data': result,
            'query_info': {
                'limit': limit,
                'offset': offset,
                'board': board or '全部',
                'timestamp': ptt_logger.get_timestamp()
            }
        })

    except ValueError as e:
        return jsonify({
            'success': False,
            'error': f'參數格式錯誤: {e}'
        }), 400
    except Exception as e:
        ptt_logger.error(f"取得文章列表 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

@api_bp.route('/graph-network', methods=['GET'])
def get_graph_network():
    """取得圖形化網絡資料

    Query Parameters:
        type (str): 查詢類型 (user-network, ip-analysis, post-interaction, multi-layer)
        username (str, optional): 使用者名稱
        ip (str, optional): IP位址
        post_id (str, optional): 文章ID
        depth (int, optional): 查詢深度，預設2
        include_ip (bool, optional): 是否包含IP關聯
        include_posts (bool, optional): 是否包含文章關聯
        include_comments (bool, optional): 是否包含推文關聯

    Returns:
        JSON: 圖形網絡資料 {
            nodes: [{ id, label, type, metadata }],
            edges: [{ source, target, label, type, weight }]
        }
    """
    try:
        query_type = request.args.get('type', 'user-network')
        username = request.args.get('username', '')
        ip = request.args.get('ip', '')
        post_id = request.args.get('post_id', '')
        depth = int(request.args.get('depth', 2))
        include_ip = request.args.get('include_ip', 'true').lower() == 'true'
        include_posts = request.args.get('include_posts', 'true').lower() == 'true'
        include_comments = request.args.get('include_comments', 'true').lower() == 'true'

        ptt_logger.info(f"API 請求: 圖形網絡查詢 - type={query_type}, username={username}, ip={ip}")

        nodes = []
        edges = []

        if query_type == 'user-network' and username:
            # 使用者網絡查詢
            result = graph_query.query_user_links(username, depth)
            if 'error' not in result:
                nodes, edges = _build_user_network_graph(result, include_ip, include_posts, include_comments)

        elif query_type == 'ip-analysis' and ip:
            # IP分析查詢
            result = graph_query.query_ip_users(ip)
            if 'error' not in result:
                nodes, edges = _build_ip_analysis_graph(result)

        elif query_type == 'post-interaction' and post_id:
            # 文章互動查詢
            result = graph_query.query_post_interactions(post_id)
            if 'error' not in result:
                nodes, edges = _build_post_interaction_graph(result)

        elif query_type == 'multi-layer' and username:
            # 多層關係查詢
            result = graph_query.analyze_user_ip_relationships(username)
            if 'error' not in result:
                nodes, edges = _build_multi_layer_graph(result, include_ip, include_posts, include_comments)

        else:
            return jsonify({
                'success': False,
                'error': '缺少必要參數或查詢類型不支援'
            }), 400

        return jsonify({
            'success': True,
            'data': {
                'nodes': nodes,
                'edges': edges,
                'stats': {
                    'node_count': len(nodes),
                    'edge_count': len(edges),
                    'user_count': len([n for n in nodes if n['type'] == 'user']),
                    'ip_count': len([n for n in nodes if n['type'] == 'ip']),
                    'post_count': len([n for n in nodes if n['type'] == 'post'])
                }
            },
            'query_info': {
                'type': query_type,
                'parameters': {
                    'username': username,
                    'ip': ip,
                    'post_id': post_id,
                    'depth': depth
                },
                'timestamp': ptt_logger.get_timestamp()
            }
        })

    except Exception as e:
        ptt_logger.error(f"圖形網絡查詢失敗: {e}")
        return jsonify({
            'success': False,
            'error': f'圖形網絡查詢失敗: {str(e)}'
        }), 500

def _build_user_network_graph(data, include_ip=True, include_posts=True, include_comments=True):
    """建構使用者網絡圖形資料"""
    nodes = []
    edges = []

    # 主要使用者節點
    main_user_id = data.get('userid', data.get('user', ''))
    if main_user_id:
        nodes.append({
            'id': main_user_id,
            'label': data.get('user', main_user_id),
            'type': 'user',
            'metadata': {
                '暱稱': data.get('nickname', ''),
                '文章數': len(data.get('posts', [])),
                '推文數': len(data.get('comments', [])),
                'IP數': len(data.get('ips', []))
            },
            'connections': len(data.get('connections', []))
        })

    # 關聯使用者節點
    for conn in data.get('connections', []):
        target_user = conn.get('target_user', '')
        target_userid = conn.get('target_userid', target_user)

        if target_userid and not any(n['id'] == target_userid for n in nodes):
            # 建立關聯使用者節點
            nodes.append({
                'id': target_userid,
                'label': target_user,  # 顯示包含暱稱的完整名稱
                'type': 'user',
                'metadata': {
                    '關聯角色': conn.get('role', ''),
                    '關聯類型': conn.get('type', ''),
                    '共同項目數': len(conn.get('shared_items', [])),
                    '暱稱': conn.get('target_nickname', '')
                },
                'connections': 1
            })

        # 關聯邊
        if target_userid:
            edge_label = conn.get('role', _get_connection_label(conn.get('type', '')))
            edge_type = conn.get('type', 'connection')

            # 根據關聯類型設定邊的方向和樣式
            if edge_type == 'i_commented_their_post':
                # 我推他的文：我 -> 他
                edges.append({
                    'source': main_user_id,
                    'target': target_userid,
                    'label': edge_label,
                    'type': 'commented_on',
                    'weight': len(conn.get('shared_items', [])) or 1,
                    'direction': 'outgoing'
                })
            elif edge_type == 'they_commented_my_post':
                # 他推我的文：他 -> 我
                edges.append({
                    'source': target_userid,
                    'target': main_user_id,
                    'label': edge_label,
                    'type': 'commented_by',
                    'weight': len(conn.get('shared_items', [])) or 1,
                    'direction': 'incoming'
                })
            elif edge_type == 'multiple_relations':
                # 多重關係：雙向邊
                edges.append({
                    'source': main_user_id,
                    'target': target_userid,
                    'label': edge_label,
                    'type': 'multiple',
                    'weight': len(conn.get('shared_items', [])) or 1,
                    'direction': 'bidirectional'
                })
            else:
                # 其他關係（IP共用、同板發文等）：無方向
                edges.append({
                    'source': main_user_id,
                    'target': target_userid,
                    'label': edge_label,
                    'type': edge_type,
                    'weight': len(conn.get('shared_items', [])) or 1,
                    'direction': 'none'
                })

    # IP節點 (如果啟用)
    if include_ip:
        for ip in data.get('ips', []):
            ip_id = f"ip_{ip}"
            nodes.append({
                'id': ip_id,
                'label': ip,
                'type': 'ip',
                'metadata': {
                    'IP位址': ip
                },
                'connections': 1
            })

            edges.append({
                'source': main_user_id,
                'target': ip_id,
                'label': '使用IP',
                'type': 'used_ip',
                'weight': 1
            })

    return nodes, edges

def _build_ip_analysis_graph(data):
    """建構IP分析圖形資料"""
    nodes = []
    edges = []

    # IP節點
    ip_address = data.get('ip', '')
    if ip_address:
        ip_id = f"ip_{ip_address}"
        nodes.append({
            'id': ip_id,
            'label': ip_address,
            'type': 'ip',
            'metadata': {
                'IP位址': ip_address,
                '使用者數': len(data.get('users', []))
            },
            'connections': len(data.get('users', []))
        })

        # 使用者節點
        for user in data.get('users', []):
            user_id = user.get('userid', user.get('username', ''))
            if user_id:
                nodes.append({
                    'id': user_id,
                    'label': user_id,
                    'type': 'user',
                    'metadata': {
                        '暱稱': user.get('nickname', ''),
                        '首次使用': user.get('first_seen', ''),
                        '最後使用': user.get('last_seen', '')
                    },
                    'connections': 1
                })

                edges.append({
                    'source': ip_id,
                    'target': user_id,
                    'label': '使用者',
                    'type': 'used_by',
                    'weight': 1
                })

    return nodes, edges

def _build_post_interaction_graph(data):
    """建構文章互動圖形資料"""
    nodes = []
    edges = []

    # 文章節點
    post_data = data.get('post', {})
    post_id = post_data.get('post_id', '')
    if post_id:
        nodes.append({
            'id': post_id,
            'label': post_data.get('title', post_id)[:30] + '...' if len(post_data.get('title', '')) > 30 else post_data.get('title', post_id),
            'type': 'post',
            'metadata': {
                '標題': post_data.get('title', ''),
                '看板': post_data.get('board', ''),
                '日期': post_data.get('date', ''),
                '作者': data.get('author', '')
            },
            'connections': len(data.get('commenters', []))
        })

        # 推文者節點
        for commenter in data.get('commenters', []):
            commenter_id = commenter.get('userid', commenter.get('username', ''))
            if commenter_id:
                nodes.append({
                    'id': commenter_id,
                    'label': commenter_id,
                    'type': 'user',
                    'metadata': {
                        '推文數': commenter.get('comment_count', 0),
                        '推文類型': ', '.join(commenter.get('comment_types', []))
                    },
                    'connections': 1
                })

                edges.append({
                    'source': commenter_id,
                    'target': post_id,
                    'label': '推文',
                    'type': 'commented',
                    'weight': commenter.get('comment_count', 1)
                })

    return nodes, edges

def _build_multi_layer_graph(data, include_ip=True, include_posts=True, include_comments=True):
    """建構多層關係圖形資料"""
    nodes = []
    edges = []

    # 主要使用者節點
    username = data.get('username', '')
    user_id = data.get('userid', username)
    if user_id:
        nodes.append({
            'id': user_id,
            'label': username,
            'type': 'user',
            'metadata': {
                '暱稱': data.get('nickname', ''),
                'IP數': len(data.get('user_ips', [])),
                '關聯使用者數': len(data.get('related_users', []))
            },
            'connections': len(data.get('user_ips', [])) + len(data.get('related_users', []))
        })

        # IP節點和關聯
        if include_ip:
            for ip_data in data.get('user_ips', []):
                ip_address = ip_data.get('ip', '')
                if ip_address:
                    ip_id = f"ip_{ip_address}"
                    if not any(n['id'] == ip_id for n in nodes):
                        nodes.append({
                            'id': ip_id,
                            'label': ip_address,
                            'type': 'ip',
                            'metadata': {
                                'IP位址': ip_address,
                                '使用日期': ip_data.get('date', ''),
                                '操作類型': ip_data.get('action', '')
                            },
                            'connections': 1
                        })

                    edges.append({
                        'source': user_id,
                        'target': ip_id,
                        'label': ip_data.get('action', '使用IP'),
                        'type': 'used_ip',
                        'weight': 1
                    })

        # 關聯使用者
        for related_user in data.get('related_users', []):
            related_id = related_user.get('userid', related_user.get('username', ''))
            if related_id and not any(n['id'] == related_id for n in nodes):
                nodes.append({
                    'id': related_id,
                    'label': related_id,
                    'type': 'user',
                    'metadata': {
                        '共用IP數': related_user.get('shared_ip_count', 0),
                        '風險等級': related_user.get('risk_level', 'low')
                    },
                    'connections': 1
                })

                edges.append({
                    'source': user_id,
                    'target': related_id,
                    'label': f"共用{related_user.get('shared_ip_count', 0)}個IP",
                    'type': 'shared_ip',
                    'weight': related_user.get('shared_ip_count', 1)
                })

    return nodes, edges

def _get_connection_label(connection_type):
    """取得關聯類型的中文標籤"""
    labels = {
        'posted_same_board': '同板發文',
        'commented_same_post': '同文推文',
        'used_same_ip': '共用IP',
        'shared_ip': '共用IP',
        'i_commented_their_post': '我推他的文',
        'they_commented_my_post': '他推我的文',
        'commented_on': '推文關係',
        'commented_by': '被推文',
        'multiple_relations': '多重關係',
        'multiple': '多重關係'
    }
    return labels.get(connection_type, connection_type)

@api_bp.route('/user-links', methods=['GET'])
def get_user_links():
    """查詢使用者互動關聯

    Query Parameters:
        username (str): 使用者名稱
        max_depth (int, optional): 查詢深度，預設3

    Returns:
        JSON: 關係網絡資料，包含節點和邊的完整資訊
    """
    try:
        username = request.args.get('username')
        max_depth = int(request.args.get('max_depth', 3))

        if not username:
            return jsonify({
                'success': False,
                'error': '缺少必要參數: username'
            }), 400

        ptt_logger.info(f"API 請求: 查詢使用者關聯 - {username}")

        # 使用簡化版查詢
        result = simple_graph_query.query_user_relationships(username, max_depth)

        if 'error' in result:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500

        # 轉換為前端需要的格式
        network_data = {
            'nodes': result['network_data']['nodes'],
            'edges': result['network_data']['edges'],
            'statistics': result['statistics'],
            'relationships': result['relationships']
        }

        return json_response({
            'success': True,
            'data': network_data,
            'query_info': {
                'username': username,
                'max_depth': max_depth,
                'timestamp': result['timestamp'],
                'query_type': 'simple_user_network'
            }
        })

    except ValueError as e:
        return jsonify({
            'success': False,
            'error': f'參數格式錯誤: {str(e)}'
        }), 400
    except Exception as e:
        ptt_logger.error(f"查詢使用者關聯 API 錯誤: {e}")
        return jsonify({
            'success': False,
            'error': '內部伺服器錯誤'
        }), 500

# CORS 支援
@api_bp.after_request
def after_request(response):
    """添加 CORS 標頭"""
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response
