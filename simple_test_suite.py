#!/usr/bin/env python3
"""
簡化版測試套件 - 避免 Unicode 問題
"""

import subprocess
import sys
import time
from datetime import datetime

def run_test(test_file, test_name):
    """執行單一測試"""
    print(f"\n{'='*60}")
    print(f"執行測試: {test_name}")
    print(f"檔案: {test_file}")
    print(f"時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print('='*60)
    
    try:
        start_time = time.time()
        result = subprocess.run([sys.executable, test_file], 
                              capture_output=True, 
                              text=True, 
                              timeout=60,
                              encoding='utf-8',
                              errors='replace')
        end_time = time.time()
        
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"[PASS] {test_name} 測試通過 ({duration:.2f}秒)")
            if result.stdout:
                # 只顯示最後幾行重要輸出
                lines = result.stdout.strip().split('\n')
                if len(lines) > 10:
                    print("...（省略部分輸出）...")
                    for line in lines[-5:]:
                        print(line)
                else:
                    print(result.stdout)
            return True
        else:
            print(f"[FAIL] {test_name} 測試失敗 ({duration:.2f}秒)")
            if result.stderr:
                print("錯誤訊息:")
                # 只顯示關鍵錯誤訊息
                lines = result.stderr.strip().split('\n')
                for line in lines[-3:]:
                    if 'UnicodeEncodeError' not in line:
                        print(line)
            return False
            
    except subprocess.TimeoutExpired:
        print(f"[TIMEOUT] {test_name} 測試逾時 (超過60秒)")
        return False
    except Exception as e:
        print(f"[ERROR] {test_name} 測試異常: {e}")
        return False

def test_cosmos_basic():
    """測試基本 Cosmos DB 功能"""
    print("\n=== 基本 Cosmos DB 測試 ===")
    
    try:
        from config import Config
        from gremlin_python.driver import client, serializer
        from gremlin_python.driver.aiohttp.transport import AiohttpTransport
        
        print("1. 配置檢查...")
        if not all([Config.COSMOS_DB_ENDPOINT, Config.COSMOS_DB_KEY, 
                   Config.COSMOS_DB_DATABASE, Config.COSMOS_DB_COLLECTION]):
            print("[FAIL] Cosmos DB 配置不完整")
            return False
        print("[PASS] 配置完整")
        
        print("2. 連接測試...")
        transport_factory = lambda: AiohttpTransport()
        gremlin_client = client.Client(
            Config.COSMOS_DB_ENDPOINT,
            'g',
            username=f"/dbs/{Config.COSMOS_DB_DATABASE}/colls/{Config.COSMOS_DB_COLLECTION}",
            password=Config.COSMOS_DB_KEY,
            message_serializer=serializer.GraphSONSerializersV2d0(),
            transport_factory=transport_factory
        )
        
        result = gremlin_client.submit("g.V().count()").all().result()
        vertex_count = result[0] if result else 0
        print(f"[PASS] 連接成功，頂點數量: {vertex_count}")
        
        gremlin_client.close()
        return True
        
    except Exception as e:
        print(f"[FAIL] 測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("PTT 圖形分析系統 - 簡化測試套件")
    print("="*60)
    print(f"開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 先測試基本功能
    if not test_cosmos_basic():
        print("\n[FAIL] 基本 Cosmos DB 測試失敗，跳過其他測試")
        return 1
    
    # 定義測試列表（只測試核心功能）
    tests = [
        ("test_cosmos_gremlin.py", "Cosmos DB Gremlin 連接"),
        ("test_graph_writer.py", "圖形資料寫入"),
        ("test_graph_query.py", "圖形查詢"),
    ]
    
    # 執行測試
    passed = 0
    failed = 0
    
    results = []
    
    for test_file, test_name in tests:
        try:
            import os
            if not os.path.exists(test_file):
                print(f"[SKIP] 跳過 {test_name}: 檔案 {test_file} 不存在")
                continue
            
            if run_test(test_file, test_name):
                passed += 1
                results.append((test_name, "PASSED"))
            else:
                failed += 1
                results.append((test_name, "FAILED"))
                
        except KeyboardInterrupt:
            print("\n\n[INTERRUPT] 測試被使用者中斷")
            break
        except Exception as e:
            print(f"\n[ERROR] 執行 {test_name} 時發生異常: {e}")
            failed += 1
            results.append((test_name, "ERROR"))
    
    # 生成測試報告
    print("\n" + "="*60)
    print("測試結果摘要")
    print("="*60)
    
    total = passed + failed
    print(f"總測試數: {total}")
    print(f"通過: {passed}")
    print(f"失敗: {failed}")
    print(f"成功率: {(passed/total*100):.1f}%" if total > 0 else "N/A")
    
    print("\n詳細結果:")
    print("-" * 60)
    for test_name, status in results:
        status_icon = "[PASS]" if status == "PASSED" else "[FAIL]"
        print(f"{status_icon} {test_name}")
    
    print(f"\n結束時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 返回適當的退出碼
    if failed > 0:
        print("\n[RESULT] 部分測試失敗")
        return 1
    else:
        print("\n[SUCCESS] 所有測試通過！")
        return 0

if __name__ == "__main__":
    exit(main())
