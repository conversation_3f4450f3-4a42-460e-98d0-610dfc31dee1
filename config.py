"""
PTT 自動登入系統 - 配置管理模組
"""
import os
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

class Config:
    """系統配置類別"""
    
    # PTT 連接設定
    PTT_USERNAME = os.getenv('PTT_USERNAME', '')
    PTT_PASSWORD = os.getenv('PTT_PASSWORD', '')
    PTT_HOST = os.getenv('PTT_HOST', 'ptt.cc')
    PTT_PORT = int(os.getenv('PTT_PORT', '23'))
    
    # 系統設定
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    TIMEZONE = os.getenv('TIMEZONE', 'Asia/Taipei')
    POST_SCHEDULE = os.getenv('POST_SCHEDULE', '08:00')
    
    # Flask 設定
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    
    # 重試設定
    MAX_RETRY_ATTEMPTS = int(os.getenv('MAX_RETRY_ATTEMPTS', '3'))
    RETRY_DELAY = int(os.getenv('RETRY_DELAY', '5'))
    
    # 連接超時設定
    CONNECTION_TIMEOUT = int(os.getenv('CONNECTION_TIMEOUT', '30'))
    LOGIN_TIMEOUT = int(os.getenv('LOGIN_TIMEOUT', '10'))

    # Azure Cosmos DB 設定
    COSMOS_DB_ENDPOINT = os.getenv('COSMOS_DB_ENDPOINT', '')
    COSMOS_DB_KEY = os.getenv('COSMOS_DB_KEY', '')
    COSMOS_DB_DATABASE = os.getenv('COSMOS_DB_DATABASE', 'ptt_graph_db')
    COSMOS_DB_COLLECTION = os.getenv('COSMOS_DB_COLLECTION', 'ptt_graph')

    # 爬蟲設定
    DEFAULT_BOARDS = os.getenv('DEFAULT_BOARDS', 'Test').split(',')
    MAX_POSTS_PER_BOARD = int(os.getenv('MAX_POSTS_PER_BOARD', '50'))
    CRAWL_INTERVAL_HOURS = int(os.getenv('CRAWL_INTERVAL_HOURS', '24'))
    
    @classmethod
    def validate_config(cls):
        """驗證必要的配置是否存在"""
        errors = []

        if not cls.PTT_USERNAME:
            errors.append("PTT_USERNAME 環境變數未設定")

        if not cls.PTT_PASSWORD:
            errors.append("PTT_PASSWORD 環境變數未設定")

        return errors

    @classmethod
    def validate_cosmos_config(cls):
        """驗證 Cosmos DB 配置"""
        errors = []

        if not cls.COSMOS_DB_ENDPOINT:
            errors.append("COSMOS_DB_ENDPOINT 環境變數未設定")

        if not cls.COSMOS_DB_KEY:
            errors.append("COSMOS_DB_KEY 環境變數未設定")

        return errors
    
    @classmethod
    def get_config_summary(cls):
        """取得配置摘要（隱藏敏感資訊）"""
        return {
            'PTT_USERNAME': cls.PTT_USERNAME[:3] + '***' if cls.PTT_USERNAME else '未設定',
            'PTT_HOST': cls.PTT_HOST,
            'PTT_PORT': cls.PTT_PORT,
            'LOG_LEVEL': cls.LOG_LEVEL,
            'TIMEZONE': cls.TIMEZONE,
            'POST_SCHEDULE': cls.POST_SCHEDULE,
            'MAX_RETRY_ATTEMPTS': cls.MAX_RETRY_ATTEMPTS,
            'CONNECTION_TIMEOUT': cls.CONNECTION_TIMEOUT,
            'COSMOS_DB_ENDPOINT': cls.COSMOS_DB_ENDPOINT[:20] + '***' if cls.COSMOS_DB_ENDPOINT else '未設定',
            'COSMOS_DB_DATABASE': cls.COSMOS_DB_DATABASE,
            'COSMOS_DB_COLLECTION': cls.COSMOS_DB_COLLECTION,
            'DEFAULT_BOARDS': cls.DEFAULT_BOARDS,
            'MAX_POSTS_PER_BOARD': cls.MAX_POSTS_PER_BOARD,
            'CRAWL_INTERVAL_HOURS': cls.CRAWL_INTERVAL_HOURS,
        }
