# 🤖 Augment AI 安全保護指南

本文檔說明如何使用 `.augmentignore` 檔案保護敏感資料，防止被 Augment AI 讀取和處理。

## 📋 目錄
1. [什麼是 .augmentignore](#什麼是-augmentignore)
2. [保護的資料類型](#保護的資料類型)
3. [檔案規則說明](#檔案規則說明)
4. [最佳實踐](#最佳實踐)
5. [驗證保護效果](#驗證保護效果)

---

## 🔍 什麼是 .augmentignore

`.augmentignore` 是一個特殊檔案，用於指定哪些檔案和目錄不應被 Augment AI 讀取。這確保：

- 🔒 **敏感資料保護**：環境變數、密碼、API 金鑰等不會被 AI 看到
- 🛡️ **隱私保護**：個人資訊、使用者資料、內部文檔受到保護
- 🚫 **防止訓練**：敏感資料不會被用於 AI 模型訓練
- 📊 **資料安全**：爬文結果、分析報告等機密資料受保護

---

## 🗂️ 保護的資料類型

### 1. 環境變數和配置
```
.env                    # 環境變數檔案
.env.*                  # 所有環境變數變體
config.ini              # 配置檔案
secrets.json            # 機密配置
credentials.json        # 認證資訊
```

### 2. PTT 相關敏感資料
```
ptt_credentials.*       # PTT 帳號密碼
crawl_results/          # 爬文結果目錄
*_crawl_*.json         # 爬文資料檔案
user_data/             # 使用者資料
user_profiles.*        # 使用者檔案
```

### 3. 資料庫相關
```
cosmos_config.*        # Azure Cosmos DB 配置
connection_strings.*   # 資料庫連接字串
*.sql                  # SQL 檔案
backup/                # 備份目錄
graph_data/            # 圖形資料庫資料
```

### 4. 日誌和除錯資料
```
logs/                  # 日誌目錄
*.log                  # 所有日誌檔案
debug_*.txt           # 除錯檔案
error_*.txt           # 錯誤記錄
```

### 5. 測試和開發資料
```
test_data/            # 測試資料
mock_data/            # 模擬資料
test_results/         # 測試結果
temp/                 # 臨時檔案
```

### 6. 部署和運維
```
.azure/               # Azure 配置
deployment_config.*   # 部署配置
*.pubxml             # 發布設定檔
k8s/                 # Kubernetes 配置
```

### 7. 個人和機密檔案
```
notes/               # 個人筆記
private/             # 私人檔案
confidential/        # 機密文檔
*.key               # 私鑰檔案
*.pem               # 憑證檔案
```

---

## 📝 檔案規則說明

### 基本語法
```bash
# 註解行
filename.ext          # 忽略特定檔案
directory/            # 忽略整個目錄
*.ext                 # 忽略所有 .ext 檔案
*pattern*             # 忽略包含 pattern 的檔案
!exception.ext        # 例外：不忽略此檔案
```

### 萬用字元模式
```bash
*secret*              # 任何包含 "secret" 的檔案
*_config.*           # 以 "_config." 結尾的檔案
test_*.json          # 以 "test_" 開頭的 JSON 檔案
logs/*.log           # logs 目錄下的所有 .log 檔案
```

### 目錄保護
```bash
sensitive_data/       # 整個目錄及其內容
cache/               # 快取目錄
temp/                # 臨時目錄
```

---

## 🛡️ 最佳實踐

### 1. 分層保護策略
```bash
# 第一層：檔案類型保護
*.env
*.key
*.log

# 第二層：目錄保護
secrets/
private/
logs/

# 第三層：模式保護
*secret*
*password*
*credential*
```

### 2. 定期檢查和更新
- 📅 **每月檢查**：確保新的敏感檔案被包含
- 🔍 **模式審查**：檢查是否有新的敏感檔案模式
- 📋 **清單更新**：根據專案發展更新保護清單

### 3. 團隊協作
- 👥 **統一標準**：團隊成員使用相同的 `.augmentignore`
- 📢 **溝通變更**：重要變更需要通知團隊
- 🔄 **版本控制**：`.augmentignore` 本身應該被版本控制

### 4. 測試保護效果
```bash
# 檢查檔案是否被忽略
echo "測試內容" > test_secret.txt
# 確認 Augment AI 無法讀取此檔案
```

---

## ✅ 驗證保護效果

### 1. 檔案測試
創建測試檔案來驗證保護：
```bash
# 創建測試敏感檔案
echo "SECRET_KEY=test123" > .env.test
echo "password=secret" > test_credentials.txt

# 檢查這些檔案是否被 Augment AI 忽略
```

### 2. 模式測試
測試萬用字元模式：
```bash
# 測試包含敏感關鍵字的檔案
echo "test" > my_secret_file.txt
echo "test" > user_password.json
echo "test" > api_key_config.yaml
```

### 3. 目錄測試
測試目錄保護：
```bash
# 創建敏感目錄
mkdir -p test_private/
echo "sensitive data" > test_private/data.txt
```

---

## 🚨 重要提醒

### ⚠️ 注意事項
1. **不要忽略 `.augmentignore` 本身**
   - 此檔案應該被版本控制
   - 團隊成員需要共享相同的保護規則

2. **避免過度保護**
   - 不要忽略必要的程式碼檔案
   - 保持開發效率和安全性的平衡

3. **定期維護**
   - 隨著專案發展更新保護規則
   - 移除不再需要的保護項目

### 🔧 故障排除
如果發現敏感資料仍被讀取：
1. 檢查 `.augmentignore` 語法
2. 確認檔案路徑正確
3. 測試萬用字元模式
4. 聯繫 Augment 支援團隊

---

## 📚 相關資源

- [Git .gitignore 文檔](https://git-scm.com/docs/gitignore)
- [安全設定指南](SECURITY_SETUP.md)
- [環境變數設定](setup_env.py)

---

## 🔄 更新記錄

- **2025-06-20**: 初始版本，包含完整的敏感資料保護規則
- **未來**: 根據專案需求和安全要求持續更新

記住：**安全是一個持續的過程，不是一次性的設定！**
