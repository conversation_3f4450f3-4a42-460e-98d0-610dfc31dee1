# 🔒 安全環境變數設定指南

本指南將幫助您安全地設定環境變數，避免敏感資訊洩露或被AI訓練使用。

## 📋 目錄
1. [本地開發環境設定](#本地開發環境設定)
2. [Azure Web App 部署設定](#azure-web-app-部署設定)
3. [安全最佳實踐](#安全最佳實踐)
4. [取得 Azure Cosmos DB 配置值](#取得-azure-cosmos-db-配置值)

---

## 🏠 本地開發環境設定

### 步驟1：創建 .env 檔案

```bash
# 在專案根目錄執行
cp .env.example .env
```

### 步驟2：編輯 .env 檔案

使用文字編輯器（如 Notepad++、VS Code）開啟 `.env` 檔案：

```bash
# 使用 VS Code 開啟
code .env

# 或使用記事本
notepad .env
```

### 步驟3：填入實際值

```env
# PTT 帳號設定（請填入您的實際帳號）
PTT_USERNAME=您的PTT帳號
PTT_PASSWORD=您的PTT密碼

# Azure Cosmos DB 設定（從 Azure Portal 取得）
COSMOS_DB_ENDPOINT=https://您的帳戶名稱.gremlin.cosmos.azure.com:443/
COSMOS_DB_KEY=您的主要或次要金鑰
COSMOS_DB_DATABASE=ptt_graph_db
COSMOS_DB_COLLECTION=ptt_graph

# Flask 安全金鑰（請產生一個隨機字串）
SECRET_KEY=請產生一個長隨機字串作為密鑰
```

### 步驟4：驗證設定

```bash
# 重新啟動應用程式
py app.py

# 檢查系統配置狀態
# 瀏覽器開啟 http://localhost:5173 > 系統配置
```

---

## ☁️ Azure Web App 部署設定

### 方法1：透過 Azure Portal 設定

1. **登入 Azure Portal**
   - 前往 https://portal.azure.com
   - 找到您的 Web App (TwLotteryBot)

2. **設定環境變數**
   - 左側選單 > 設定 > 組態
   - 應用程式設定 > 新增應用程式設定

3. **添加以下設定**：
   ```
   名稱: PTT_USERNAME          值: 您的PTT帳號
   名稱: PTT_PASSWORD          值: 您的PTT密碼
   名稱: COSMOS_DB_ENDPOINT    值: https://您的帳戶.gremlin.cosmos.azure.com:443/
   名稱: COSMOS_DB_KEY         值: 您的Cosmos DB金鑰
   名稱: COSMOS_DB_DATABASE    值: ptt_graph_db
   名稱: COSMOS_DB_COLLECTION  值: ptt_graph
   名稱: SECRET_KEY            值: 隨機產生的密鑰
   ```

4. **儲存設定**
   - 點擊「儲存」
   - 應用程式會自動重新啟動

### 方法2：使用 Azure CLI

```bash
# 設定資源群組和應用程式名稱
$resourceGroup = "您的資源群組名稱"
$appName = "TwLotteryBot"

# 設定環境變數
az webapp config appsettings set --resource-group $resourceGroup --name $appName --settings `
  PTT_USERNAME="您的PTT帳號" `
  PTT_PASSWORD="您的PTT密碼" `
  COSMOS_DB_ENDPOINT="https://您的帳戶.gremlin.cosmos.azure.com:443/" `
  COSMOS_DB_KEY="您的Cosmos DB金鑰" `
  COSMOS_DB_DATABASE="ptt_graph_db" `
  COSMOS_DB_COLLECTION="ptt_graph" `
  SECRET_KEY="隨機產生的密鑰"
```

---

## 🛡️ 安全最佳實踐

### 1. 檔案安全
- ✅ `.env` 檔案已在 `.gitignore` 中
- ✅ `.augmentignore` 保護敏感資料不被 AI 讀取
- ✅ 絕不將 `.env` 檔案提交到版本控制
- ✅ 不要在程式碼中硬編碼敏感資訊
- ✅ 使用 `.env.example` 作為範本

### 2. AI 安全保護
- 🤖 `.augmentignore` 檔案防止 Augment AI 讀取敏感資料
- 🤖 保護環境變數、日誌檔案、測試資料
- 🤖 防止個人資訊和機密配置被AI訓練使用
- 🤖 確保爬文結果和使用者資料的隱私

### 3. 密碼安全
- 🔐 使用強密碼（至少12個字符，包含大小寫、數字、符號）
- 🔐 定期更換密碼
- 🔐 不要在多個服務使用相同密碼

### 4. 金鑰管理
- 🔑 定期輪換 Cosmos DB 金鑰
- 🔑 使用主要金鑰進行部署，保留次要金鑰作為備用
- 🔑 監控金鑰使用情況

### 5. 存取控制
- 👥 限制能存取環境變數的人員
- 👥 使用 Azure RBAC 控制 Cosmos DB 存取權限
- 👥 啟用 Azure 活動記錄監控

---

## 🗝️ 取得 Azure Cosmos DB 配置值

### 步驟1：登入 Azure Portal
1. 前往 https://portal.azure.com
2. 搜尋並選擇您的 Cosmos DB 帳戶

### 步驟2：取得連接資訊
1. **端點 (COSMOS_DB_ENDPOINT)**
   - 左側選單 > 概觀
   - 複製「Gremlin 端點」
   - 格式：`https://您的帳戶名稱.gremlin.cosmos.azure.com:443/`

2. **金鑰 (COSMOS_DB_KEY)**
   - 左側選單 > 金鑰
   - 複製「主要金鑰」或「次要金鑰」

3. **資料庫和集合名稱**
   - 左側選單 > 資料總管
   - 確認資料庫名稱：`ptt_graph_db`
   - 確認集合名稱：`ptt_graph`

### 步驟3：測試連接
```bash
# 設定環境變數後重新啟動應用程式
py app.py

# 檢查連接狀態
# 瀏覽器開啟 http://localhost:5173 > 系統配置
```

---

## 🚀 快速設定腳本

創建一個設定腳本來簡化流程：

```bash
# setup_env.bat (Windows)
@echo off
echo 正在設定環境變數...

set /p PTT_USER="請輸入PTT帳號: "
set /p PTT_PASS="請輸入PTT密碼: "
set /p COSMOS_ENDPOINT="請輸入Cosmos DB端點: "
set /p COSMOS_KEY="請輸入Cosmos DB金鑰: "

echo PTT_USERNAME=%PTT_USER% > .env
echo PTT_PASSWORD=%PTT_PASS% >> .env
echo COSMOS_DB_ENDPOINT=%COSMOS_ENDPOINT% >> .env
echo COSMOS_DB_KEY=%COSMOS_KEY% >> .env
echo COSMOS_DB_DATABASE=ptt_graph_db >> .env
echo COSMOS_DB_COLLECTION=ptt_graph >> .env

echo 環境變數設定完成！
echo 請重新啟動應用程式。
pause
```

---

## ⚠️ 重要提醒

1. **絕不要**將 `.env` 檔案分享給他人
2. **絕不要**將敏感資訊貼到聊天室或論壇
3. **絕不要**將環境變數寫在程式碼註解中
4. **定期檢查** `.gitignore` 確保 `.env` 被排除
5. **使用不同的密碼**用於不同的服務

---

## 🆘 如果不小心洩露了怎麼辦？

1. **立即更換密碼**
   - PTT 密碼：登入 PTT 更改密碼
   - Cosmos DB 金鑰：Azure Portal > 金鑰 > 重新產生金鑰

2. **檢查存取記錄**
   - Azure Portal > 活動記錄
   - 查看是否有異常存取

3. **更新所有部署**
   - 本地 `.env` 檔案
   - Azure Web App 環境變數

記住：安全性是持續的過程，不是一次性的設定！
