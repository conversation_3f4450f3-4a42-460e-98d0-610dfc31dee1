@echo off
chcp 65001 >nul
title PTT 自動登入系統 - 環境變數設定

echo.
echo 🔒 PTT 自動登入系統 - 安全環境變數設定
echo ==================================================
echo 此工具將幫助您安全地設定環境變數
echo 所有敏感資訊都會儲存在 .env 檔案中，不會出現在程式碼裡
echo.

REM 檢查是否已存在 .env 檔案
if exist .env (
    echo ⚠️  發現現有的 .env 檔案
    set /p "overwrite=是否要覆蓋現有設定？(y/N): "
    if /i not "%overwrite%"=="y" (
        echo 設定已取消
        pause
        exit /b
    )
    echo.
)

echo 📝 PTT 帳號設定
echo --------------------
set /p "PTT_USERNAME=PTT 使用者名稱: "
if "%PTT_USERNAME%"=="" (
    echo ❌ 使用者名稱不能為空
    pause
    exit /b
)

echo.
echo ☁️  Azure Cosmos DB 設定
echo -------------------------
echo 請從 Azure Portal ^> Cosmos DB ^> 金鑰 頁面取得以下資訊
echo.

set /p "COSMOS_DB_ENDPOINT=Cosmos DB 端點 (Gremlin Endpoint): "
if "%COSMOS_DB_ENDPOINT%"=="" (
    echo ❌ 端點不能為空
    pause
    exit /b
)

echo.
echo 🗄️  資料庫設定
echo ---------------
set /p "COSMOS_DB_DATABASE=資料庫名稱 [ptt_graph_db]: "
if "%COSMOS_DB_DATABASE%"=="" set "COSMOS_DB_DATABASE=ptt_graph_db"

set /p "COSMOS_DB_COLLECTION=集合名稱 [ptt_graph]: "
if "%COSMOS_DB_COLLECTION%"=="" set "COSMOS_DB_COLLECTION=ptt_graph"

echo.
echo 💾 正在建立 .env 檔案...

REM 建立 .env 檔案
(
echo # PTT 自動登入系統 - 環境變數設定
echo # 此檔案包含敏感資訊，請勿分享或提交到版本控制
echo # 建立時間: %date% %time%
echo.
echo # PTT 連線設定
echo PTT_USERNAME=%PTT_USERNAME%
echo PTT_PASSWORD=請手動編輯此檔案填入您的PTT密碼
echo PTT_HOST=ptt.cc
echo PTT_PORT=23
echo.
echo # Azure Cosmos DB 設定
echo COSMOS_DB_ENDPOINT=%COSMOS_DB_ENDPOINT%
echo COSMOS_DB_KEY=請手動編輯此檔案填入您的Cosmos DB金鑰
echo COSMOS_DB_DATABASE=%COSMOS_DB_DATABASE%
echo COSMOS_DB_COLLECTION=%COSMOS_DB_COLLECTION%
echo.
echo # 系統設定
echo SECRET_KEY=請手動編輯此檔案填入隨機產生的密鑰
echo LOG_LEVEL=INFO
echo TIMEZONE=Asia/Taipei
echo POST_SCHEDULE=08:00
echo.
echo # 效能設定
echo MAX_RETRY_ATTEMPTS=3
echo CONNECTION_TIMEOUT=30
echo LOGIN_TIMEOUT=10
echo RETRY_DELAY=5
echo.
echo # 爬蟲設定
echo DEFAULT_BOARDS=Test
echo MAX_POSTS_PER_BOARD=50
echo CRAWL_INTERVAL_HOURS=24
echo.
echo # Flask 設定
echo FLASK_DEBUG=False
echo FLASK_ENV=production
) > .env

echo ✅ .env 檔案已建立！
echo.
echo ⚠️  重要：請手動編輯 .env 檔案完成設定
echo    1. 填入您的 PTT 密碼
echo    2. 填入您的 Cosmos DB 金鑰
echo    3. 產生並填入 Flask SECRET_KEY
echo.
echo 🔒 安全提醒:
echo    • .env 檔案已建立，包含您的敏感資訊
echo    • 此檔案已在 .gitignore 中，不會被提交到版本控制
echo    • 請勿將此檔案分享給他人
echo    • 定期更換密碼和金鑰
echo.
echo 🚀 下一步:
echo    1. 編輯 .env 檔案完成設定
echo    2. 重新啟動應用程式: py app.py
echo    3. 開啟瀏覽器檢查系統配置狀態
echo    4. 如需部署到 Azure，請參考 SECURITY_SETUP.md
echo.

REM 詢問是否要開啟 .env 檔案進行編輯
set /p "edit_now=是否要現在開啟 .env 檔案進行編輯？(y/N): "
if /i "%edit_now%"=="y" (
    echo 正在開啟 .env 檔案...
    notepad .env
)

echo.
echo 設定完成！請完成 .env 檔案編輯後重新啟動應用程式。
pause
