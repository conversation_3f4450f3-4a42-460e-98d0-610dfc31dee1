#!/usr/bin/env python3
"""
自動測試 PTT 登入和取得使用者資訊功能
"""

import sys
import os
from datetime import datetime

# 添加當前目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ptt_client import PTTClient
from config import Config
from logger import ptt_logger

def test_ptt_login_and_user_info():
    """測試 PTT 登入和取得使用者資訊"""
    print("=" * 60)
    print("PTT 自動登入和使用者資訊測試")
    print("=" * 60)
    
    # 檢查配置
    print("1. 檢查配置...")
    config_errors = Config.validate_config()
    if config_errors:
        print("❌ 配置錯誤:")
        for error in config_errors:
            print(f"   - {error}")
        return False
    
    print(f"✅ 配置正確")
    print(f"   PTT 使用者: {Config.PTT_USERNAME}")
    print(f"   PTT 主機: {Config.PTT_HOST}:{Config.PTT_PORT}")
    
    # 建立 PTT 客戶端
    print("\n2. 建立 PTT 客戶端...")
    client = PTTClient()
    
    try:
        # 執行完整的登入測試
        print("\n3. 執行登入測試...")
        result = client.test_login()
        
        print(f"\n測試結果: {'成功' if result['success'] else '失敗'}")
        print(f"訊息: {result['message']}")
        print(f"時間: {result['timestamp']}")
        
        print("\n詳細步驟:")
        for i, detail in enumerate(result['details'], 1):
            print(f"   {i}. {detail}")
        
        return result['success']
        
    except Exception as e:
        print(f"❌ 測試過程發生錯誤: {e}")
        return False

def test_get_user_api():
    """直接測試 get_user API"""
    print("\n" + "=" * 60)
    print("直接測試 PyPtt get_user API")
    print("=" * 60)
    
    try:
        import PyPtt
        
        print("1. 建立 PyPtt API...")
        ptt_bot = PyPtt.API()
        
        print("2. 登入 PTT...")
        ptt_bot.login(
            ptt_id=Config.PTT_USERNAME,
            ptt_pw=Config.PTT_PASSWORD,
            kick_other_session=True
        )
        print("✅ 登入成功")
        
        print("3. 測試 get_user 方法...")
        
        # 測試不同的調用方式
        test_cases = [
            ("get_user(username)", lambda: ptt_bot.get_user(Config.PTT_USERNAME)),
            ("get_user() 無參數", lambda: ptt_bot.get_user()),
        ]
        
        for test_name, test_func in test_cases:
            try:
                print(f"\n   測試: {test_name}")
                user_info = test_func()
                
                print(f"   ✅ 成功取得使用者資訊")
                print(f"   資料類型: {type(user_info)}")
                
                if isinstance(user_info, dict):
                    print("   字典內容:")
                    for key, value in user_info.items():
                        print(f"     {key}: {value}")
                else:
                    print("   物件屬性:")
                    attrs = [attr for attr in dir(user_info) if not attr.startswith('_')]
                    for attr in attrs[:10]:  # 只顯示前10個屬性
                        try:
                            value = getattr(user_info, attr)
                            print(f"     {attr}: {value}")
                        except:
                            print(f"     {attr}: <無法取得>")
                
                break  # 找到可用的方法就停止
                
            except Exception as e:
                print(f"   ❌ 失敗: {e}")
        
        print("\n4. 登出...")
        ptt_bot.logout()
        print("✅ 登出成功")
        
        return True
        
    except Exception as e:
        print(f"❌ API 測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print(f"開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 測試1: 使用我們的 PTTClient
    success1 = test_ptt_login_and_user_info()
    
    # 測試2: 直接測試 PyPtt API
    success2 = test_get_user_api()
    
    print("\n" + "=" * 60)
    print("測試總結")
    print("=" * 60)
    print(f"PTTClient 測試: {'✅ 成功' if success1 else '❌ 失敗'}")
    print(f"PyPtt API 測試: {'✅ 成功' if success2 else '❌ 失敗'}")
    
    if success1 and success2:
        print("\n🎉 所有測試都成功！")
        return 0
    else:
        print("\n⚠ 部分測試失敗，請檢查上述錯誤訊息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
