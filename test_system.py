#!/usr/bin/env python3
"""
PTT 自動登入系統 - 系統測試腳本
用於驗證系統各模組是否正常運作
"""

import sys
import os

def test_imports():
    """測試模組匯入"""
    print("=== 測試模組匯入 ===")
    
    try:
        import flask
        print("✓ Flask 匯入成功")
    except ImportError as e:
        print(f"✗ Flask 匯入失敗: {e}")
        return False
    
    try:
        import PyPtt
        print("✓ PyPtt 匯入成功")
    except ImportError as e:
        print(f"✗ PyPtt 匯入失敗: {e}")
        return False
    
    try:
        from config import Config
        print("✓ Config 模組匯入成功")
    except ImportError as e:
        print(f"✗ Config 模組匯入失敗: {e}")
        return False
    
    try:
        from logger import ptt_logger
        print("✓ Logger 模組匯入成功")
    except ImportError as e:
        print(f"✗ Logger 模組匯入失敗: {e}")
        return False
    
    try:
        from ptt_client import PTTClient
        print("✓ PTTClient 模組匯入成功")
    except ImportError as e:
        print(f"✗ PTTClient 模組匯入失敗: {e}")
        return False
    
    return True

def test_config():
    """測試配置模組"""
    print("\n=== 測試配置模組 ===")
    
    try:
        from config import Config
        
        # 測試配置摘要
        summary = Config.get_config_summary()
        print(f"✓ 配置摘要取得成功: {len(summary)} 項設定")
        
        # 測試配置驗證
        errors = Config.validate_config()
        if errors:
            print(f"⚠ 配置驗證發現問題: {len(errors)} 個錯誤")
            for error in errors:
                print(f"  - {error}")
        else:
            print("✓ 配置驗證通過")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置模組測試失敗: {e}")
        return False

def test_logger():
    """測試日誌模組"""
    print("\n=== 測試日誌模組 ===")
    
    try:
        from logger import ptt_logger
        
        # 測試各種日誌等級
        ptt_logger.info("測試資訊日誌")
        ptt_logger.warning("測試警告日誌")
        ptt_logger.debug("測試除錯日誌")
        
        print("✓ 日誌模組測試成功")
        return True
        
    except Exception as e:
        print(f"✗ 日誌模組測試失敗: {e}")
        return False

def test_ptt_client():
    """測試PTT客戶端模組"""
    print("\n=== 測試PTT客戶端模組 ===")
    
    try:
        from ptt_client import PTTClient
        
        # 建立客戶端實例
        client = PTTClient()
        print("✓ PTTClient 實例建立成功")
        
        # 測試狀態取得
        status = client.get_status()
        print(f"✓ 狀態取得成功: {status}")
        
        return True
        
    except Exception as e:
        print(f"✗ PTT客戶端模組測試失敗: {e}")
        return False

def test_flask_app():
    """測試Flask應用程式"""
    print("\n=== 測試Flask應用程式 ===")
    
    try:
        from app import app
        
        # 測試應用程式建立
        print("✓ Flask 應用程式建立成功")
        
        # 測試路由
        with app.test_client() as client:
            # 測試首頁
            response = client.get('/')
            if response.status_code == 200:
                print("✓ 首頁路由測試成功")
            else:
                print(f"⚠ 首頁路由返回狀態碼: {response.status_code}")
            
            # 測試健康檢查
            response = client.get('/health')
            if response.status_code == 200:
                print("✓ 健康檢查路由測試成功")
            else:
                print(f"⚠ 健康檢查路由返回狀態碼: {response.status_code}")
            
            # 測試狀態端點
            response = client.get('/status')
            if response.status_code == 200:
                print("✓ 狀態端點測試成功")
            else:
                print(f"⚠ 狀態端點返回狀態碼: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"✗ Flask應用程式測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("PTT 自動登入系統 - 系統測試")
    print("=" * 50)
    
    tests = [
        ("模組匯入", test_imports),
        ("配置模組", test_config),
        ("日誌模組", test_logger),
        ("PTT客戶端", test_ptt_client),
        ("Flask應用", test_flask_app),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name} 測試發生異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"測試結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 所有測試都通過了！系統準備就緒。")
        return 0
    else:
        print("⚠ 部分測試失敗，請檢查上述錯誤訊息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
