#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試編碼修正
"""

import sys
import json
import PyPtt
from config import Config

# 設定正確的編碼
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')
if hasattr(sys.stderr, 'reconfigure'):
    sys.stderr.reconfigure(encoding='utf-8')

def test_post_encoding():
    """測試文章編碼"""
    print("=" * 60)
    print("PTT 文章編碼測試")
    print("=" * 60)
    
    try:
        # 建立 PTT API 並登入
        ptt_bot = PyPtt.API()
        ptt_bot.login(
            ptt_id=Config.PTT_USERNAME,
            ptt_pw=Config.PTT_PASSWORD,
            kick_other_session=True
        )
        print("✅ PTT 登入成功")
        
        # 取得看板最新索引
        board_name = "Test"
        newest_index = ptt_bot.get_newest_index(
            index_type=PyPtt.NewIndex.BOARD,
            board=board_name
        )
        print(f"✅ 最新索引: {newest_index}")
        
        # 取得最近幾篇文章並檢查編碼
        for i in range(max(1, newest_index - 5), newest_index + 1):
            print(f"\n--- 檢查文章 {i} ---")
            try:
                post = ptt_bot.get_post(
                    board=board_name,
                    index=i
                )
                
                if post:
                    print(f"文章物件類型: {type(post)}")
                    
                    # 檢查所有屬性
                    if hasattr(post, '__dict__'):
                        print("文章屬性:")
                        for attr_name, attr_value in post.__dict__.items():
                            print(f"  {attr_name}: {repr(attr_value)} (類型: {type(attr_value)})")
                    
                    # 檢查常見屬性的編碼
                    attrs_to_check = ['title', 'author', 'date', 'content']
                    for attr in attrs_to_check:
                        if hasattr(post, attr):
                            value = getattr(post, attr)
                            print(f"{attr}: {repr(value)}")
                            
                            # 如果是字串，檢查是否包含中文
                            if isinstance(value, str) and value:
                                try:
                                    # 嘗試編碼為 UTF-8
                                    encoded = value.encode('utf-8')
                                    decoded = encoded.decode('utf-8')
                                    print(f"  UTF-8 編碼測試: {decoded}")
                                except Exception as e:
                                    print(f"  編碼錯誤: {e}")
                    
                    # 如果找到有內容的文章就停止
                    if hasattr(post, 'title') and getattr(post, 'title'):
                        break
                        
                else:
                    print(f"文章 {i} 不存在")
                    
            except Exception as e:
                print(f"取得文章 {i} 失敗: {e}")
        
        # 登出
        ptt_bot.logout()
        print("\n✅ PTT 登出成功")
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()

def test_json_encoding():
    """測試 JSON 編碼"""
    print("\n" + "=" * 60)
    print("JSON 編碼測試")
    print("=" * 60)
    
    # 測試中文資料的 JSON 序列化
    test_data = {
        "title": "測試文章標題",
        "author": "測試作者",
        "content": "這是測試內容，包含中文字符",
        "board": "測試看板"
    }
    
    try:
        # 測試 JSON 序列化
        json_str = json.dumps(test_data, ensure_ascii=False, indent=2)
        print("JSON 序列化成功:")
        print(json_str)
        
        # 測試 JSON 反序列化
        parsed_data = json.loads(json_str)
        print("\nJSON 反序列化成功:")
        for key, value in parsed_data.items():
            print(f"  {key}: {value}")
            
    except Exception as e:
        print(f"❌ JSON 編碼測試失敗: {e}")

if __name__ == "__main__":
    test_post_encoding()
    test_json_encoding()
