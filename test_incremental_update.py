#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試增量更新功能
"""

import sys
from graph_writer import GraphWriter

# 設定正確的編碼
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')

def test_incremental_update():
    """測試增量更新功能"""
    print("=" * 60)
    print("測試增量更新功能")
    print("=" * 60)
    
    try:
        # 建立圖形寫入器
        writer = GraphWriter()
        
        # 第一次寫入：初始資料
        print("1. 第一次寫入初始資料...")
        initial_data = {
            'vertices': {
                'users': [
                    {
                        'id': 'testuser001 (測試使用者)',
                        'properties': {
                            'first_seen': '2025-06-19T10:00:00',
                            'last_seen': '2025-06-19T10:30:00'
                        }
                    }
                ],
                'ips': [
                    {
                        'id': '*************',
                        'properties': {
                            'first_seen': '2025-06-19T10:00:00',
                            'last_seen': '2025-06-19T10:30:00'
                        }
                    }
                ],
                'posts': [
                    {
                        'id': 'Test.Incremental001',
                        'properties': {
                            'title': '測試增量更新的文章',
                            'board': 'Test',
                            'date': '2025-06-19T10:30:00',
                            'content_length': 100  # 初始內容長度
                        }
                    }
                ]
            },
            'edges': {
                'posted': [
                    {
                        'from': 'testuser001 (測試使用者)',
                        'to': 'Test.Incremental001',
                        'properties': {
                            'date': '2025-06-19T10:30:00',
                            'board': 'Test'
                        }
                    }
                ],
                'used_ip': [
                    {
                        'from': 'testuser001 (測試使用者)',
                        'to': '*************',
                        'properties': {
                            'date': '2025-06-19T10:30:00',
                            'action': 'posted'
                        }
                    }
                ],
                'from_ip': [
                    {
                        'from': 'Test.Incremental001',
                        'to': '*************',
                        'properties': {
                            'date': '2025-06-19T10:30:00'
                        }
                    }
                ],
                'commented': [
                    {
                        'from': 'testuser001 (測試使用者)',
                        'to': 'Test.Incremental001',
                        'properties': {
                            'comment_type': '推',
                            'time': '2025-06-19T10:35:00',
                            'content': '這是第一條推文',
                            'ip': '*************'
                        }
                    }
                ]
            }
        }
        
        result1 = writer.write_graph_data(initial_data, incremental=True)
        if result1:
            print("✅ 初始資料寫入成功")
        else:
            print("❌ 初始資料寫入失敗")
            return False
        
        # 第二次寫入：模擬重複資料和新推文
        print("\n2. 第二次寫入：模擬重複資料和新推文...")
        updated_data = {
            'vertices': {
                'users': [
                    {
                        'id': 'testuser001 (測試使用者)',  # 重複使用者
                        'properties': {
                            'first_seen': '2025-06-19T10:00:00',
                            'last_seen': '2025-06-19T11:00:00'  # 更新最後活動時間
                        }
                    },
                    {
                        'id': 'testuser002 (新使用者)',  # 新使用者
                        'properties': {
                            'first_seen': '2025-06-19T10:45:00',
                            'last_seen': '2025-06-19T10:45:00'
                        }
                    }
                ],
                'ips': [
                    {
                        'id': '*************',  # 重複 IP
                        'properties': {
                            'first_seen': '2025-06-19T10:00:00',
                            'last_seen': '2025-06-19T11:00:00'
                        }
                    },
                    {
                        'id': '*************',  # 新 IP
                        'properties': {
                            'first_seen': '2025-06-19T10:45:00',
                            'last_seen': '2025-06-19T10:45:00'
                        }
                    }
                ],
                'posts': [
                    {
                        'id': 'Test.Incremental001',  # 重複文章，但內容增加
                        'properties': {
                            'title': '測試增量更新的文章',
                            'board': 'Test',
                            'date': '2025-06-19T10:30:00',
                            'content_length': 150  # 內容長度增加
                        }
                    }
                ]
            },
            'edges': {
                'posted': [
                    {
                        'from': 'testuser001 (測試使用者)',
                        'to': 'Test.Incremental001',
                        'properties': {
                            'date': '2025-06-19T10:30:00',
                            'board': 'Test'
                        }
                    }
                ],
                'used_ip': [
                    {
                        'from': 'testuser001 (測試使用者)',
                        'to': '*************',
                        'properties': {
                            'date': '2025-06-19T11:00:00',
                            'action': 'commented'
                        }
                    },
                    {
                        'from': 'testuser002 (新使用者)',
                        'to': '*************',
                        'properties': {
                            'date': '2025-06-19T10:45:00',
                            'action': 'commented'
                        }
                    }
                ],
                'from_ip': [
                    {
                        'from': 'Test.Incremental001',
                        'to': '*************',  # IP 變更！
                        'properties': {
                            'date': '2025-06-19T10:45:00'
                        }
                    }
                ],
                'commented': [
                    {
                        'from': 'testuser001 (測試使用者)',
                        'to': 'Test.Incremental001',
                        'properties': {
                            'comment_type': '推',
                            'time': '2025-06-19T10:35:00',  # 重複推文
                            'content': '這是第一條推文',
                            'ip': '*************'
                        }
                    },
                    {
                        'from': 'testuser001 (測試使用者)',
                        'to': 'Test.Incremental001',
                        'properties': {
                            'comment_type': '推',
                            'time': '2025-06-19T11:00:00',  # 新推文
                            'content': '這是第二條推文',
                            'ip': '*************'
                        }
                    },
                    {
                        'from': 'testuser002 (新使用者)',
                        'to': 'Test.Incremental001',
                        'properties': {
                            'comment_type': '→',
                            'time': '2025-06-19T10:45:00',  # 新推文
                            'content': '新使用者的推文',
                            'ip': '*************'
                        }
                    }
                ]
            }
        }
        
        result2 = writer.write_graph_data(updated_data, incremental=True)
        if result2:
            print("✅ 增量更新成功")
        else:
            print("❌ 增量更新失敗")
            return False
        
        print("\n3. 測試完成！")
        print("預期結果:")
        print("  - 重複的使用者和文章應該被更新而不是重複建立")
        print("  - 新推文應該被正確識別和記錄")
        print("  - IP 變更應該被追蹤")
        print("  - 系統應該記錄哪些是新資料，哪些是更新")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_incremental_update()
    
    if success:
        print("\n🎉 增量更新功能測試成功！")
        print("\n主要功能:")
        print("✅ 重複資料檢查")
        print("✅ 新推文識別")
        print("✅ IP 變更追蹤")
        print("✅ 內容更新檢測")
    else:
        print("\n❌ 增量更新功能測試失敗")
